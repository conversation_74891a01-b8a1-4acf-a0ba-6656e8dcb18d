# Prompt Tools Chrome扩展安装指南

## 安装步骤

### 1. 准备工作
确保您已经启动了Prompt Tools服务器：
```bash
# 在项目根目录运行
npm start
# 或
node server/src/app.js
```

服务器应该运行在 `http://localhost:18080`

### 2. 加载扩展到Chrome

#### 方法一：开发者模式加载（推荐）
1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择 `chrome-extension` 文件夹
6. 扩展将出现在扩展列表中

#### 方法二：拖拽安装
1. 打开 `chrome://extensions/` 页面
2. 开启"开发者模式"
3. 将整个 `chrome-extension` 文件夹拖拽到页面中

### 3. 验证安装
- 在Chrome工具栏中应该能看到Prompt Tools图标
- 点击图标应该能打开扩展弹窗
- 如果服务器正在运行，应该能看到提示词列表

## 功能测试

### 测试界面预览
在浏览器中打开 `chrome-extension/test-popup.html` 可以预览界面效果。

### 基本功能测试
1. **连接测试**：打开扩展，检查是否能加载提示词
2. **分类测试**：点击不同分类按钮，验证筛选功能
3. **搜索测试**：在搜索框输入关键词测试搜索
4. **复制测试**：点击复制按钮，检查剪贴板内容
5. **编辑测试**：点击编辑按钮，验证编辑页面打开
6. **创建测试**：点击创建按钮，测试新建功能
7. **删除测试**：点击删除按钮，验证确认对话框

## 配置说明

### 服务器配置
扩展会自动读取 `server-config.js` 中的配置：

```javascript
// server-config.js
window.PromptToolsConfig = {
  currentEnvironment: 'development',
  environments: {
    development: {
      baseUrl: 'http://localhost:18080',
      apiUrl: 'http://localhost:18080/api'
    }
  }
};
```

### 自定义配置
如果您的服务器运行在不同端口，请修改 `server-config.js` 文件。

## 故障排除

### 常见问题

#### 1. 扩展无法加载提示词
**症状**：打开扩展显示"连接失败"
**解决方案**：
- 确认服务器正在运行
- 检查服务器地址配置
- 查看浏览器控制台错误信息

#### 2. 编辑页面无法打开
**症状**：点击编辑按钮没有反应
**解决方案**：
- 检查浏览器是否阻止了弹窗
- 确认扩展权限设置正确
- 重新加载扩展

#### 3. 分类筛选不工作
**症状**：点击分类按钮没有筛选效果
**解决方案**：
- 检查提示词是否有正确的标签
- 确认分类映射配置正确
- 重新加载扩展

#### 4. 样式显示异常
**症状**：界面样式混乱或图标不显示
**解决方案**：
- 检查网络连接（需要加载外部字体和图标）
- 清除浏览器缓存
- 重新加载扩展

### 调试方法

#### 查看扩展日志
1. 打开 `chrome://extensions/`
2. 找到Prompt Tools扩展
3. 点击"详细信息"
4. 点击"检查视图"中的相关链接
5. 在开发者工具中查看控制台日志

#### 检查网络请求
1. 打开扩展弹窗
2. 按F12打开开发者工具
3. 切换到Network标签
4. 重新加载扩展，观察API请求

## 开发说明

### 热重载功能
扩展包含热重载功能，修改代码后会自动重新加载：

```bash
# 启动开发服务器
npm run dev:extension
```

### 文件监控
开发模式下，以下文件的修改会触发自动重载：
- `popup.html`
- `popup.css`
- `popup.js`
- `edit.html`
- `edit.css`
- `edit.js`
- `manifest.json`

### 构建生产版本
```bash
# 构建生产版本
npm run build:extension
```

## 更新扩展

### 手动更新
1. 下载最新版本的代码
2. 在 `chrome://extensions/` 页面点击扩展的"重新加载"按钮
3. 或者删除旧扩展，重新加载新版本

### 自动更新（开发模式）
开发模式下，扩展会自动检测文件变化并重新加载。

## 支持与反馈

如果您在使用过程中遇到问题：

1. **查看日志**：检查浏览器控制台和扩展日志
2. **检查配置**：确认服务器配置正确
3. **重新安装**：尝试删除并重新安装扩展
4. **提交反馈**：在项目仓库中提交Issue

## 许可证

本扩展遵循与主项目相同的许可证。
