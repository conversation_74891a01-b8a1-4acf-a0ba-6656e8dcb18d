// Prompt Tools Firefox Extension - Popup Script
class PromptToolsPopup {
  constructor() {
    // 使用配置文件中的API基础URL
    this.config = window.PromptToolsConfig || {};
    this.apiBase = this.config.getApiBaseUrl ? this.config.getApiBaseUrl() : 'http://localhost:18080/api';
    this.requestConfig = this.config.getRequestConfig ? this.config.getRequestConfig() : {};

    this.prompts = [];
    this.filteredPrompts = [];
    this.currentCategory = 'all';
    this.contextMenuTarget = null;
    this.currentView = 'list'; // 'list' or 'edit'
    this.editingPrompt = null;
    this.isNewPrompt = false;
    this.availableTags = []; // 从API获取的所有标签
    this.categoryButtons = []; // 动态生成的分类按钮

    console.log('Firefox弹窗脚本初始化 - 当前环境:', this.config.currentEnvironment);
    console.log('API基础URL:', this.apiBase);

    this.init();
  }

  async init() {
    this.bindEvents();
    await this.loadTags();
    await this.loadPrompts();
  }

  bindEvents() {
    // 检查必要的DOM元素是否存在
    const requiredElements = [
      'createBtn', 'refreshBtn', 'openWebBtn', 'retryBtn',
      'searchInput', 'searchBtn', 'backToListBtn', 'savePromptBtn',
      'editForm', 'editPromptContent', 'copyAndEditBtn',
      'confirmCancel', 'confirmDelete', 'confirmDialog'
    ];

    for (const elementId of requiredElements) {
      const element = document.getElementById(elementId);
      if (!element) {
        console.error(`缺少必要的DOM元素: ${elementId}`);
      }
    }

    // 创建按钮
    document.getElementById('createBtn').addEventListener('click', () => {
      this.showEditView();
    });

    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.loadPrompts();
    });

    // 打开网页版按钮 - 使用browser API
    document.getElementById('openWebBtn').addEventListener('click', () => {
      // 使用配置文件中的基础URL
      const baseUrl = this.config.getCurrentEnvironment ?
        this.config.getCurrentEnvironment().baseUrl :
        'http://localhost:18080';

      console.log('打开 Prompt Tools 网页版:', baseUrl);
      browser.tabs.create({ url: baseUrl });
    });

    // 重试按钮
    document.getElementById('retryBtn').addEventListener('click', () => {
      this.loadPrompts();
    });

    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');

    searchInput.addEventListener('input', (e) => {
      this.filterPrompts(e.target.value);
    });

    searchBtn.addEventListener('click', () => {
      this.filterPrompts(searchInput.value);
    });

    // 回车搜索
    searchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.filterPrompts(e.target.value);
      }
    });

    // 编辑视图事件
    document.getElementById('backToListBtn').addEventListener('click', () => {
      this.showListView();
    });

    document.getElementById('savePromptBtn').addEventListener('click', () => {
      this.savePrompt();
    });

    // 复制并编辑按钮
    document.getElementById('copyAndEditBtn').addEventListener('click', () => {
      this.copyAndEdit();
    });

    // 确认对话框事件
    document.getElementById('confirmCancel').addEventListener('click', () => {
      this.hideConfirmDialog();
    });

    document.getElementById('confirmDelete').addEventListener('click', () => {
      this.confirmDelete();
    });
  }

  async loadPrompts() {
    try {
      this.showLoading();
      
      // 使用配置文件中的API端点
      const apiUrl = this.config.getApiEndpoint ?
        this.config.getApiEndpoint('prompts', 'list') :
        `${this.apiBase}/prompts`;

      console.log('加载提示词列表:', apiUrl);

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      this.prompts = Array.isArray(data) ? data : (data.prompts || []);
      this.filteredPrompts = [...this.prompts];
      
      console.log(`成功加载 ${this.prompts.length} 个提示词`);
      
      this.generateCategoryButtons();
      this.renderPrompts();
      this.hideLoading();
      
    } catch (error) {
      console.error('加载提示词失败:', error);
      this.showError('无法连接到 Prompt Tools 服务器\n请确保服务器正在运行：http://localhost:18080');
    }
  }

  async loadTags() {
    try {
      // 使用配置文件中的API端点
      const apiUrl = this.config.getApiEndpoint ?
        this.config.getApiEndpoint('tags', 'list') :
        `${this.apiBase}/prompts/meta/tags`;

      console.log('加载标签列表:', apiUrl);

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: headers
      });

      if (response.ok) {
        const data = await response.json();
        this.availableTags = Array.isArray(data) ? data : (data.tags || []);
        console.log(`成功加载 ${this.availableTags.length} 个标签`);
      } else {
        console.warn('加载标签失败，使用默认标签');
        this.availableTags = ['通用', '编程', '写作', '分析'];
      }
    } catch (error) {
      console.warn('加载标签失败:', error);
      this.availableTags = ['通用', '编程', '写作', '分析'];
    }
  }

  async copyPrompt(content) {
    try {
      // 使用现代剪贴板API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(content);
      } else {
        // 降级方案：使用传统方法
        const textArea = document.createElement('textarea');
        textArea.value = content;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }
      
      this.showStatusMessage('✅ 已复制到剪贴板', 'success');
      console.log('提示词已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      this.showStatusMessage('❌ 复制失败', 'error');
    }
  }

  showLoading() {
    document.getElementById('loadingSpinner').classList.remove('hidden');
    document.getElementById('promptsList').classList.add('hidden');
    document.getElementById('errorMessage').classList.add('hidden');
    document.getElementById('emptyState').classList.add('hidden');
  }

  hideLoading() {
    document.getElementById('loadingSpinner').classList.add('hidden');
  }

  showError(message) {
    this.hideLoading();
    document.getElementById('errorMessage').classList.remove('hidden');
    document.getElementById('promptsList').classList.add('hidden');
    document.getElementById('emptyState').classList.add('hidden');
    
    // 更新错误消息
    const errorText = document.querySelector('#errorMessage .error-text p');
    if (errorText) {
      errorText.textContent = message;
    }
  }

  showStatusMessage(message, type = 'info') {
    const statusElement = document.getElementById('statusMessage');
    statusElement.textContent = message;
    statusElement.className = `status-message status-${type}`;
    statusElement.classList.remove('hidden');
    
    setTimeout(() => {
      statusElement.classList.add('hidden');
    }, 3000);
  }

  filterPrompts(searchTerm) {
    const term = searchTerm.toLowerCase().trim();
    
    if (!term) {
      this.filteredPrompts = [...this.prompts];
    } else {
      this.filteredPrompts = this.prompts.filter(prompt => {
        return prompt.name.toLowerCase().includes(term) ||
               prompt.content.toLowerCase().includes(term) ||
               (prompt.tags && prompt.tags.some(tag => tag.toLowerCase().includes(term)));
      });
    }
    
    this.renderPrompts();
  }

  generateCategoryButtons() {
    // 从提示词中提取所有标签
    const allTags = new Set();
    this.prompts.forEach(prompt => {
      if (prompt.tags && Array.isArray(prompt.tags)) {
        prompt.tags.forEach(tag => allTags.add(tag));
      }
    });

    // 生成分类按钮
    const categoryContainer = document.getElementById('categoryButtons');
    if (!categoryContainer) return;

    categoryContainer.innerHTML = '';

    // 添加"全部"按钮
    const allButton = document.createElement('button');
    allButton.className = 'category-btn active';
    allButton.textContent = '全部';
    allButton.addEventListener('click', () => {
      this.filterByCategory('all');
      this.updateCategoryButtons(allButton);
    });
    categoryContainer.appendChild(allButton);

    // 添加标签按钮
    Array.from(allTags).sort().forEach(tag => {
      const button = document.createElement('button');
      button.className = 'category-btn';
      button.textContent = tag;
      button.addEventListener('click', () => {
        this.filterByCategory(tag);
        this.updateCategoryButtons(button);
      });
      categoryContainer.appendChild(button);
    });
  }

  filterByCategory(category) {
    this.currentCategory = category;
    
    if (category === 'all') {
      this.filteredPrompts = [...this.prompts];
    } else {
      this.filteredPrompts = this.prompts.filter(prompt => {
        return prompt.tags && prompt.tags.includes(category);
      });
    }
    
    this.renderPrompts();
  }

  updateCategoryButtons(activeButton) {
    const buttons = document.querySelectorAll('.category-btn');
    buttons.forEach(btn => btn.classList.remove('active'));
    activeButton.classList.add('active');
  }

  renderPrompts() {
    const promptsList = document.getElementById('promptsList');
    const emptyState = document.getElementById('emptyState');

    if (this.filteredPrompts.length === 0) {
      promptsList.classList.add('hidden');
      emptyState.classList.remove('hidden');
      return;
    }

    emptyState.classList.add('hidden');
    promptsList.classList.remove('hidden');

    promptsList.innerHTML = '';

    this.filteredPrompts.forEach((prompt, index) => {
      const promptElement = this.createPromptElement(prompt, index);
      promptsList.appendChild(promptElement);
    });
  }

  createPromptElement(prompt, index) {
    const div = document.createElement('div');
    div.className = 'prompt-item';
    div.style.animationDelay = `${index * 0.1}s`;

    // 限制内容预览长度
    const maxPreviewLength = this.config.extensionConfig?.ui?.maxPromptPreview || 100;
    const contentPreview = prompt.content.length > maxPreviewLength
      ? prompt.content.substring(0, maxPreviewLength) + '...'
      : prompt.content;

    div.innerHTML = `
      <div class="prompt-header">
        <h3 class="prompt-title">${this.escapeHtml(prompt.name)}</h3>
        <div class="prompt-actions">
          <button class="btn-icon edit-btn" title="编辑">
            <i class="material-icons">edit</i>
          </button>
          <button class="btn-icon delete-btn" title="删除">
            <i class="material-icons">delete</i>
          </button>
        </div>
      </div>
      <div class="prompt-content">${this.escapeHtml(contentPreview)}</div>
      ${prompt.tags ? `<div class="prompt-tags">
        ${prompt.tags.map(tag => `<span class="tag">${this.escapeHtml(tag)}</span>`).join('')}
      </div>` : ''}
      <div class="prompt-meta">
        <span class="prompt-source">${prompt.source || '未知来源'}</span>
        <span class="prompt-date">${this.formatDate(prompt.createdAt || prompt.created_at)}</span>
      </div>
    `;

    // 点击复制
    div.addEventListener('click', (e) => {
      if (!e.target.closest('.prompt-actions')) {
        this.copyPrompt(prompt.content);
      }
    });

    // 编辑按钮
    const editBtn = div.querySelector('.edit-btn');
    editBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.editPrompt(prompt);
    });

    // 删除按钮
    const deleteBtn = div.querySelector('.delete-btn');
    deleteBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.showDeleteConfirm(prompt);
    });

    return div;
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  formatDate(dateString) {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return '';
    }
  }

  showEditView(prompt = null) {
    this.currentView = 'edit';
    this.editingPrompt = prompt;
    this.isNewPrompt = !prompt;

    document.getElementById('listView').classList.add('hidden');
    document.getElementById('editView').classList.remove('hidden');

    // 填充表单
    if (prompt) {
      document.getElementById('editPromptName').value = prompt.name || '';
      document.getElementById('editPromptContent').value = prompt.content || '';
      document.getElementById('editPromptTags').value = prompt.tags ? prompt.tags.join(', ') : '';
      document.getElementById('editPromptNotes').value = prompt.notes || '';
    } else {
      // 清空表单
      document.getElementById('editPromptName').value = '';
      document.getElementById('editPromptContent').value = '';
      document.getElementById('editPromptTags').value = '';
      document.getElementById('editPromptNotes').value = '';
    }

    // 聚焦到名称输入框
    setTimeout(() => {
      document.getElementById('editPromptName').focus();
    }, 100);
  }

  showListView() {
    this.currentView = 'list';
    document.getElementById('editView').classList.add('hidden');
    document.getElementById('listView').classList.remove('hidden');
  }

  editPrompt(prompt) {
    this.showEditView(prompt);
  }

  async savePrompt() {
    const name = document.getElementById('editPromptName').value.trim();
    const content = document.getElementById('editPromptContent').value.trim();
    const tagsInput = document.getElementById('editPromptTags').value.trim();
    const notes = document.getElementById('editPromptNotes').value.trim();

    if (!name || !content) {
      this.showStatusMessage('请填写提示词名称和内容', 'error');
      return;
    }

    const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

    const promptData = {
      name,
      content,
      tags,
      notes
    };

    try {
      let apiUrl, method;

      if (this.isNewPrompt) {
        // 创建新提示词
        apiUrl = this.config.getApiEndpoint ?
          this.config.getApiEndpoint('prompts', 'create') :
          `${this.apiBase}/prompts`;
        method = 'POST';
      } else {
        // 更新现有提示词
        apiUrl = this.config.getApiEndpoint ?
          this.config.getApiEndpoint('prompts', 'update', { id: this.editingPrompt.id }) :
          `${this.apiBase}/prompts/${this.editingPrompt.id}`;
        method = 'PUT';
      }

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      const response = await fetch(apiUrl, {
        method,
        headers,
        body: JSON.stringify(promptData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      this.showStatusMessage(this.isNewPrompt ? '✅ 提示词创建成功' : '✅ 提示词更新成功', 'success');
      this.showListView();
      await this.loadPrompts(); // 重新加载列表

    } catch (error) {
      console.error('保存提示词失败:', error);
      this.showStatusMessage('❌ 保存失败: ' + error.message, 'error');
    }
  }

  showDeleteConfirm(prompt) {
    this.promptToDelete = prompt;
    document.getElementById('confirmDialog').classList.remove('hidden');
    document.querySelector('#confirmDialog .confirm-message').textContent =
      `确定要删除提示词 "${prompt.name}" 吗？`;
  }

  hideConfirmDialog() {
    document.getElementById('confirmDialog').classList.add('hidden');
    this.promptToDelete = null;
  }

  async confirmDelete() {
    if (!this.promptToDelete) return;

    try {
      const apiUrl = this.config.getApiEndpoint ?
        this.config.getApiEndpoint('prompts', 'delete', { id: this.promptToDelete.id }) :
        `${this.apiBase}/prompts/${this.promptToDelete.id}`;

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      const response = await fetch(apiUrl, {
        method: 'DELETE',
        headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      this.showStatusMessage('✅ 提示词删除成功', 'success');
      this.hideConfirmDialog();
      await this.loadPrompts(); // 重新加载列表

    } catch (error) {
      console.error('删除提示词失败:', error);
      this.showStatusMessage('❌ 删除失败: ' + error.message, 'error');
    }
  }

  copyAndEdit() {
    // 获取当前选中的文本
    browser.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      browser.tabs.sendMessage(tabs[0].id, { action: 'getSelectedText' }, (response) => {
        if (response && response.selectedText) {
          this.showEditView();
          document.getElementById('editPromptContent').value = response.selectedText;
          document.getElementById('editPromptName').value = this.generatePromptName(response.selectedText);
        } else {
          this.showStatusMessage('请先在网页中选择文本', 'warning');
        }
      });
    });
  }

  generatePromptName(content) {
    const words = content.trim().split(/\s+/).slice(0, 8).join(' ');
    const maxLength = 50;

    if (words.length <= maxLength) {
      return words;
    }

    return words.substring(0, maxLength - 3) + '...';
  }
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
  new PromptToolsPopup();
});
