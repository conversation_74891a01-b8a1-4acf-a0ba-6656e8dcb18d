# Chrome Extension 开发环境指南

## 🚀 快速开始

### 1. 启动开发环境

**方法一：使用 npm 脚本（推荐）**
```bash
# 在项目根目录执行
npm run extension:dev
```

**方法二：直接启动**
```bash
# 进入扩展目录
cd chrome-extension

# 启动开发服务器
node start-dev.js
```

**方法三：Windows 批处理**
```bash
# 双击运行
start-dev.bat
```

### 2. 安装 Chrome 扩展

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `chrome-extension` 文件夹
6. 扩展安装完成！

## 🔥 热更新功能

### 自动重载
- ✅ 修改任何扩展文件后会自动重载
- ✅ 无需手动刷新扩展
- ✅ 实时查看代码变化效果

### 支持的文件类型
- `manifest.json` - 扩展配置
- `*.js` - JavaScript 文件
- `*.html` - HTML 文件
- `*.css` - 样式文件
- `server-config.js` - 服务器配置

### 热重载服务器
- 🌐 地址: `http://localhost:8080`
- 📊 状态检查: `http://localhost:8080/status`
- 🔄 手动重载: `http://localhost:8080/reload`

## 🛠️ 开发工具

### 调试方法

**1. 弹窗调试**
- 右键点击扩展图标
- 选择"检查弹出式窗口"
- 在开发者工具中调试 popup.js

**2. 后台脚本调试**
- 访问 `chrome://extensions/`
- 找到 Prompt Tools 扩展
- 点击"service worker"链接
- 在开发者工具中调试 background.js

**3. 内容脚本调试**
- 在任意网页按 F12 打开开发者工具
- 在 Console 标签中查看 content.js 的日志
- 使用 `console.log()` 进行调试

### 快捷命令

```bash
# 检查热重载服务器状态
npm run extension:status

# 手动触发扩展重载
npm run extension:reload

# 查看服务器日志
curl http://localhost:8080/status | jq
```

## 📁 项目结构

```
chrome-extension/
├── manifest.json          # 扩展配置文件
├── server-config.js       # 服务器配置
├── hot-reload.js          # 🆕 热重载脚本
├── dev-server.js          # 🆕 开发服务器
├── start-dev.js           # 🆕 开发环境启动脚本
├── start-dev.bat          # 🆕 Windows 启动脚本
├── background.js          # 后台脚本
├── content.js             # 内容脚本
├── popup.html/css/js      # 弹窗界面
├── icons/                 # 扩展图标
└── DEV_GUIDE.md          # 🆕 开发指南
```

## ⚙️ 配置说明

### 热重载配置

在 `hot-reload.js` 中可以配置：

```javascript
class ExtensionHotReload {
  constructor() {
    this.checkInterval = 1000;  // 检查间隔（毫秒）
    this.reloadServer = 'http://localhost:8080';  // 服务器地址
  }
}
```

### 开发服务器配置

在 `dev-server.js` 中可以配置：

```javascript
const server = new ExtensionDevServer({
  port: 8080,                    // 服务器端口
  watchDir: __dirname,           // 监听目录
  excludePatterns: [             // 排除模式
    /node_modules/,
    /\.git/,
    /\.DS_Store/
  ]
});
```

## 🔧 故障排除

### 常见问题

**1. 扩展无法加载**
- 检查 `manifest.json` 语法是否正确
- 确保所有引用的文件都存在
- 查看 Chrome 扩展管理页面的错误信息

**2. 热重载不工作**
- 确认开发服务器正在运行 (`http://localhost:8080/status`)
- 检查浏览器控制台是否有错误
- 尝试手动重载: `npm run extension:reload`

**3. 服务器连接失败**
- 确认主应用服务器正在运行 (`http://localhost:18080`)
- 检查 `server-config.js` 中的配置
- 验证网络连接

**4. 端口冲突**
- 修改 `dev-server.js` 中的端口配置
- 或者关闭占用端口的其他程序

### 调试技巧

**1. 查看详细日志**
```bash
# 在 chrome-extension 目录下
node dev-server.js
```

**2. 检查文件监听**
```bash
# 查看哪些文件被监听
curl http://localhost:8080/status | jq '.watchDir'
```

**3. 手动测试重载**
```bash
# 触发重载并查看响应
curl -v http://localhost:8080/reload
```

## 📝 开发最佳实践

### 1. 代码组织
- 将配置集中在 `server-config.js`
- 使用模块化的方式组织代码
- 添加适当的错误处理

### 2. 调试策略
- 使用 `console.log()` 进行调试
- 利用 Chrome 开发者工具
- 定期检查扩展权限

### 3. 性能优化
- 避免频繁的 DOM 操作
- 合理使用缓存
- 优化 API 请求频率

### 4. 安全考虑
- 验证用户输入
- 使用 HTTPS（生产环境）
- 限制权限范围

## 🚀 部署准备

### 打包扩展
```bash
# 创建发布版本（移除开发工具）
cp -r chrome-extension chrome-extension-release
cd chrome-extension-release
rm dev-server.js start-dev.js start-dev.bat DEV_GUIDE.md
# 编辑 manifest.json 移除 hot-reload.js 引用
```

### 发布检查清单
- [ ] 移除开发工具文件
- [ ] 更新版本号
- [ ] 测试所有功能
- [ ] 检查权限设置
- [ ] 优化图标和描述

---

## 📞 获取帮助

如果遇到问题，请：
1. 查看浏览器控制台错误
2. 检查开发服务器日志
3. 参考 Chrome 扩展开发文档
4. 联系开发团队

祝您开发愉快！🎉
