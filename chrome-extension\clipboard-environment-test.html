<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>剪贴板环境测试工具</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    .test-section {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    .test-button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
      font-size: 14px;
    }
    .test-button:hover {
      background: #0056b3;
    }
    .test-result {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      margin: 10px 0;
      min-height: 50px;
      font-family: monospace;
      font-size: 12px;
      white-space: pre-wrap;
    }
    .success { border-color: #28a745; background: #d4edda; color: #155724; }
    .error { border-color: #dc3545; background: #f8d7da; color: #721c24; }
    .warning { border-color: #ffc107; background: #fff3cd; color: #856404; }
    .info { border-color: #17a2b8; background: #d1ecf1; color: #0c5460; }
    
    .env-info {
      background: #e3f2fd;
      border: 1px solid #2196f3;
      padding: 15px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }
    .status-ok { background: #28a745; }
    .status-warning { background: #ffc107; }
    .status-error { background: #dc3545; }
    
    kbd {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 3px;
      padding: 2px 4px;
      font-size: 11px;
    }
  </style>
</head>
<body>
  <h1>🔧 剪贴板环境测试工具</h1>
  
  <div class="test-section">
    <h2>📋 当前环境信息</h2>
    <div id="environmentInfo" class="env-info">
      正在检测环境...
    </div>
  </div>

  <div class="test-section">
    <h2>🧪 剪贴板功能测试</h2>
    
    <h3>测试内容：</h3>
    <div style="background: #e3f2fd; padding: 10px; border-radius: 4px; margin: 10px 0;">
      <strong>测试文本：</strong>这是一个用于测试剪贴板功能的示例文本，包含中文、English、数字123和特殊符号!@#$%
    </div>
    
    <h3>测试按钮：</h3>
    <button class="test-button" onclick="testClipboardAPI()">测试 Clipboard API</button>
    <button class="test-button" onclick="testExecCommand()">测试 execCommand</button>
    <button class="test-button" onclick="testSmartCopy()">测试智能复制策略</button>
    <button class="test-button" onclick="clearResults()">清除结果</button>
    
    <h3>测试结果：</h3>
    <div id="testResults" class="test-result">
      点击上面的按钮开始测试...
    </div>
    
    <h3>粘贴验证区域：</h3>
    <textarea style="width: 100%; height: 80px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" 
              placeholder="在这里按 Ctrl+V 粘贴，验证复制是否成功..."></textarea>
  </div>

  <div class="test-section">
    <h2>🔍 问题诊断</h2>
    <div id="diagnosis" class="test-result info">
      等待测试结果...
    </div>
  </div>

  <div class="test-section">
    <h2>💡 修复建议</h2>
    <div id="recommendations" class="test-result warning">
      等待环境检测完成...
    </div>
  </div>

  <script>
    const testText = '这是一个用于测试剪贴板功能的示例文本，包含中文、English、数字123和特殊符号!@#$%';
    
    // 检测环境信息
    function detectEnvironment() {
      const env = {
        protocol: window.location.protocol,
        hostname: window.location.hostname,
        port: window.location.port,
        isSecureContext: window.isSecureContext,
        hasClipboard: !!navigator.clipboard,
        hasWriteText: !!(navigator.clipboard && navigator.clipboard.writeText),
        hasExecCommand: !!document.execCommand,
        userAgent: navigator.userAgent,
        isLocalhost: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
        isHTTPS: window.location.protocol === 'https:',
        isExtension: window.location.protocol === 'chrome-extension:' || window.location.protocol === 'moz-extension:'
      };
      
      return env;
    }
    
    // 显示环境信息
    function showEnvironmentInfo() {
      const env = detectEnvironment();
      const infoDiv = document.getElementById('environmentInfo');
      
      const secureStatus = env.isSecureContext ? 
        '<span class="status-indicator status-ok"></span>安全上下文' : 
        '<span class="status-indicator status-error"></span>非安全上下文';
        
      const clipboardStatus = env.hasWriteText ? 
        '<span class="status-indicator status-ok"></span>Clipboard API 可用' : 
        '<span class="status-indicator status-error"></span>Clipboard API 不可用';
        
      const execStatus = env.hasExecCommand ? 
        '<span class="status-indicator status-ok"></span>execCommand 可用' : 
        '<span class="status-indicator status-error"></span>execCommand 不可用';
      
      infoDiv.innerHTML = `
        <strong>协议：</strong>${env.protocol}<br>
        <strong>主机：</strong>${env.hostname}${env.port ? ':' + env.port : ''}<br>
        <strong>安全上下文：</strong>${secureStatus}<br>
        <strong>Clipboard API：</strong>${clipboardStatus}<br>
        <strong>execCommand：</strong>${execStatus}<br>
        <strong>是否本地：</strong>${env.isLocalhost ? '✅ 是' : '❌ 否'}<br>
        <strong>是否HTTPS：</strong>${env.isHTTPS ? '✅ 是' : '❌ 否'}<br>
        <strong>是否扩展：</strong>${env.isExtension ? '✅ 是' : '❌ 否'}
      `;
      
      // 生成诊断和建议
      generateDiagnosis(env);
      generateRecommendations(env);
    }
    
    // 生成诊断信息
    function generateDiagnosis(env) {
      const diagnosisDiv = document.getElementById('diagnosis');
      let diagnosis = '';
      
      if (env.isSecureContext && env.hasWriteText) {
        diagnosis = '✅ 环境完全支持现代剪贴板功能\n所有复制方法都应该正常工作。';
      } else if (env.hasExecCommand) {
        diagnosis = '⚠️ 环境部分支持剪贴板功能\nClipboard API 不可用，但 execCommand 可以使用。\n建议优先使用 execCommand 方法。';
      } else {
        diagnosis = '❌ 环境不支持自动剪贴板功能\n需要使用手动复制方案。';
      }
      
      if (!env.isSecureContext) {
        diagnosis += '\n\n⚠️ 当前环境不是安全上下文：\n';
        if (!env.isHTTPS && !env.isLocalhost) {
          diagnosis += '- 使用 HTTP 协议且非 localhost\n';
        }
        diagnosis += '- 这会限制 Clipboard API 的使用';
      }
      
      diagnosisDiv.textContent = diagnosis;
    }
    
    // 生成修复建议
    function generateRecommendations(env) {
      const recDiv = document.getElementById('recommendations');
      let recommendations = '';
      
      if (env.isSecureContext && env.hasWriteText) {
        recommendations = '✅ 当前环境最佳，无需修复。';
      } else {
        recommendations = '🔧 修复建议：\n\n';
        
        if (!env.isSecureContext) {
          recommendations += '1. 升级到安全上下文：\n';
          if (!env.isHTTPS && !env.isLocalhost) {
            recommendations += '   - 将服务器升级到 HTTPS\n';
            recommendations += '   - 或使用 localhost 进行开发\n';
          }
        }
        
        recommendations += '2. 代码层面修复：\n';
        recommendations += '   - 优先使用 execCommand 方法\n';
        recommendations += '   - 实现多层级复制策略\n';
        recommendations += '   - 提供手动复制备用方案\n\n';
        
        recommendations += '3. 扩展特定修复：\n';
        recommendations += '   - 确保 manifest.json 包含 clipboardWrite 权限\n';
        recommendations += '   - 在扩展环境中使用特权 API\n';
        recommendations += '   - 实现环境检测和智能降级';
      }
      
      recDiv.textContent = recommendations;
    }
    
    // 显示测试结果
    function showResult(message, type = 'info') {
      const results = document.getElementById('testResults');
      const timestamp = new Date().toLocaleTimeString();
      results.className = `test-result ${type}`;
      results.textContent = `[${timestamp}] ${message}`;
    }
    
    // 测试 Clipboard API
    async function testClipboardAPI() {
      showResult('正在测试 Clipboard API...', 'info');
      
      try {
        if (!navigator.clipboard) {
          showResult('❌ Clipboard API 不可用\n浏览器不支持 navigator.clipboard', 'error');
          return;
        }
        
        if (!navigator.clipboard.writeText) {
          showResult('❌ writeText 方法不可用\n当前环境不支持 writeText 方法', 'error');
          return;
        }
        
        await navigator.clipboard.writeText(testText);
        showResult('✅ Clipboard API 测试成功！\n文本已复制到剪贴板，请在粘贴区域验证。', 'success');
        
      } catch (error) {
        showResult(`❌ Clipboard API 测试失败\n错误: ${error.message}\n原因: ${getErrorReason(error)}`, 'error');
      }
    }
    
    // 测试 execCommand
    function testExecCommand() {
      showResult('正在测试 execCommand...', 'info');
      
      try {
        if (!document.execCommand) {
          showResult('❌ execCommand 不可用\n浏览器不支持 document.execCommand', 'error');
          return;
        }
        
        const textArea = document.createElement('textarea');
        textArea.value = testText;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        textArea.style.opacity = '0';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const success = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (success) {
          showResult('✅ execCommand 测试成功！\n文本已复制到剪贴板，请在粘贴区域验证。', 'success');
        } else {
          showResult('❌ execCommand 测试失败\nexecCommand 返回 false', 'error');
        }
        
      } catch (error) {
        showResult(`❌ execCommand 测试失败\n错误: ${error.message}`, 'error');
      }
    }
    
    // 测试智能复制策略
    async function testSmartCopy() {
      showResult('正在测试智能复制策略...', 'info');
      
      let copySuccess = false;
      let method = '';
      
      // 策略1: 尝试 execCommand
      try {
        const textArea = document.createElement('textarea');
        textArea.value = testText;
        textArea.style.position = 'fixed';
        textArea.style.left = '0';
        textArea.style.top = '0';
        textArea.style.width = '1px';
        textArea.style.height = '1px';
        textArea.style.opacity = '0';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const success = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (success) {
          copySuccess = true;
          method = 'execCommand';
        }
      } catch (error) {
        console.warn('execCommand 失败:', error);
      }
      
      // 策略2: 如果 execCommand 失败，尝试 Clipboard API
      if (!copySuccess && navigator.clipboard && navigator.clipboard.writeText) {
        try {
          await navigator.clipboard.writeText(testText);
          copySuccess = true;
          method = 'Clipboard API';
        } catch (error) {
          console.warn('Clipboard API 失败:', error);
        }
      }
      
      if (copySuccess) {
        showResult(`✅ 智能复制策略成功！\n使用方法: ${method}\n文本已复制到剪贴板，请在粘贴区域验证。`, 'success');
      } else {
        showResult('❌ 智能复制策略失败\n所有复制方法都不可用，需要手动复制。', 'error');
      }
    }
    
    // 获取错误原因
    function getErrorReason(error) {
      if (error.name === 'NotAllowedError') {
        return '权限被拒绝，可能是用户拒绝了剪贴板访问';
      } else if (error.message.includes('secure context')) {
        return '需要安全上下文 (HTTPS 或 localhost)';
      } else if (error.message.includes('user activation')) {
        return '需要用户激活 (用户交互)';
      } else {
        return '未知原因';
      }
    }
    
    // 清除结果
    function clearResults() {
      const results = document.getElementById('testResults');
      results.className = 'test-result';
      results.textContent = '结果已清除，点击按钮开始新的测试...';
    }
    
    // 页面加载时显示环境信息
    showEnvironmentInfo();
    
    console.log('剪贴板环境测试工具已加载');
    console.log('环境信息:', detectEnvironment());
  </script>
</body>
</html>
