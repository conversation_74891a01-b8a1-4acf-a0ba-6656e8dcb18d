<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Hub 图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 10px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            color: white;
            background: linear-gradient(135deg, #007bff, #0056b3);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .icon-16 { width: 16px; height: 16px; font-size: 8px; }
        .icon-32 { width: 32px; height: 32px; font-size: 16px; }
        .icon-48 { width: 48px; height: 48px; font-size: 24px; }
        .icon-128 { width: 128px; height: 128px; font-size: 64px; }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background 0.2s;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .canvas-container {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Prompt Tools 图标生成器</h1>
        
        <div class="icon-preview">
            <div class="icon-item">
                <div class="icon icon-16">P</div>
                <div>16x16</div>
                <button class="download-btn" onclick="downloadIcon(16)">下载</button>
            </div>
            <div class="icon-item">
                <div class="icon icon-32">P</div>
                <div>32x32</div>
                <button class="download-btn" onclick="downloadIcon(32)">下载</button>
            </div>
            <div class="icon-item">
                <div class="icon icon-48">PT</div>
                <div>48x48</div>
                <button class="download-btn" onclick="downloadIcon(48)">下载</button>
            </div>
            <div class="icon-item">
                <div class="icon icon-128">PT</div>
                <div>128x128</div>
                <button class="download-btn" onclick="downloadIcon(128)">下载</button>
            </div>
        </div>

        <div class="instructions">
            <h3>📋 使用说明</h3>
            <div class="step">
                <strong>步骤 1：</strong> 点击上方的"下载"按钮，下载对应尺寸的图标
            </div>
            <div class="step">
                <strong>步骤 2：</strong> 将下载的图标文件重命名为：
                <ul>
                    <li>16x16 → <code>icon16.png</code></li>
                    <li>32x32 → <code>icon32.png</code></li>
                    <li>48x48 → <code>icon48.png</code></li>
                    <li>128x128 → <code>icon128.png</code></li>
                </ul>
            </div>
            <div class="step">
                <strong>步骤 3：</strong> 将所有图标文件放入 <code>chrome-extension/icons/</code> 目录
            </div>
            <div class="step">
                <strong>步骤 4：</strong> 在 Chrome 中加载扩展程序
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="iconCanvas"></canvas>
        </div>
    </div>

    <script>
        function downloadIcon(size) {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // 设置画布尺寸
            canvas.width = size;
            canvas.height = size;
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#007bff');
            gradient.addColorStop(1, '#0056b3');
            
            // 绘制圆角矩形背景
            const radius = size * 0.2;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            if (size <= 32) {
                ctx.font = `bold ${size * 0.5}px Arial`;
                ctx.fillText('P', size / 2, size / 2);
            } else {
                ctx.font = `bold ${size * 0.25}px Arial`;
                ctx.fillText('PT', size / 2, size / 2);
            }
            
            // 下载图片
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
        
        // 添加 roundRect 方法支持（如果浏览器不支持）
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
