# Chrome 扩展 "Failed to fetch" 错误修复指南

## 🐛 问题描述

Chrome 扩展中出现错误：**"添加失败：Failed to fetch"**

## 🔍 问题分析

根据您的配置文件分析，发现以下问题：

### 1. 当前配置状态
- **当前环境**: `testing` 
- **服务器地址**: `http://************:18080`
- **问题**: 该测试服务器可能无法访问

### 2. 可能的原因
1. **网络连接问题**: 测试服务器 `************:18080` 无法访问
2. **CORS 跨域问题**: 服务器未配置正确的跨域头
3. **服务器未运行**: 目标服务器可能已停止服务
4. **防火墙阻止**: 网络防火墙阻止了连接
5. **权限问题**: 扩展缺少必要的网络权限

## 🔧 修复方案

### 方案一：切换到本地开发环境（推荐）

1. **修改配置文件**：
   打开 `chrome-extension/server-config.js`，将第55行修改为：
   ```javascript
   const CURRENT_ENVIRONMENT = 'development';  // 从 'testing' 改为 'development'
   ```

2. **确保本地服务器运行**：
   ```bash
   cd e:\Project\Prompt-Tools-main
   npm start
   # 或
   node server/src/app.js
   ```
   
3. **验证服务器**：
   在浏览器中访问 `http://localhost:18080` 确认服务器正常运行

### 方案二：修复测试服务器连接

如果您需要使用测试服务器，请检查：

1. **网络连接**：
   ```bash
   # 在命令行中测试连接
   ping ************
   curl http://************:18080/api/health
   ```

2. **VPN 或网络设置**：
   - 确保您在正确的网络环境中
   - 检查是否需要 VPN 连接

### 方案三：使用自定义服务器

1. **修改配置**：
   ```javascript
   const CURRENT_ENVIRONMENT = 'custom';
   
   // 在 SERVER_ENVIRONMENTS.custom 中设置您的服务器地址
   SERVER_ENVIRONMENTS.custom.baseUrl = 'http://your-server:port';
   ```

## 🛠️ 详细修复步骤

### 步骤1：检查当前配置
```javascript
// 在 chrome-extension/server-config.js 第55行
const CURRENT_ENVIRONMENT = 'testing';  // 当前设置
```

### 步骤2：修改为开发环境
```javascript
// 修改为：
const CURRENT_ENVIRONMENT = 'development';
```

### 步骤3：重新加载扩展
1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 找到 Prompt Hub 扩展
4. 点击刷新按钮 🔄

### 步骤4：测试功能
1. 在任意网页选中文本
2. 右键选择"添加到 Prompt Hub"
3. 应该看到成功提示

## 🔍 调试方法

### 1. 检查扩展控制台
1. 访问 `chrome://extensions/`
2. 找到 Prompt Hub 扩展
3. 点击"检查视图"中的"背景页"
4. 查看控制台错误信息

### 2. 检查网络请求
在扩展控制台中查看：
```javascript
// 应该看到类似的日志
发送提示词到服务器: http://localhost:18080/api/prompts
```

### 3. 手动测试 API
在浏览器中访问：
```
http://localhost:18080/api/health
```
应该返回服务器状态信息。

## ⚠️ 常见错误和解决方案

### 错误1：Failed to fetch
**原因**: 服务器无法访问
**解决**: 切换到 `development` 环境并启动本地服务器

### 错误2：CORS error
**原因**: 跨域请求被阻止
**解决**: 确保服务器配置了正确的 CORS 头

### 错误3：Network error
**原因**: 网络连接问题
**解决**: 检查网络连接和防火墙设置

### 错误4：404 Not Found
**原因**: API 端点不存在
**解决**: 检查服务器是否正确启动和 API 路径配置

## 📋 快速检查清单

- [ ] 修改 `CURRENT_ENVIRONMENT` 为 `'development'`
- [ ] 启动本地服务器 (`npm start`)
- [ ] 验证服务器运行 (`http://localhost:18080`)
- [ ] 重新加载 Chrome 扩展
- [ ] 测试添加功能
- [ ] 检查扩展控制台无错误

## 🎯 推荐配置

对于日常使用，推荐以下配置：

```javascript
// server-config.js
const CURRENT_ENVIRONMENT = 'development';

// 开发环境配置
development: {
  name: '开发环境',
  baseUrl: 'http://localhost:18080',
  apiPath: '/api',
  timeout: 10000,
  retryAttempts: 3,
  description: '本地开发服务器'
}
```

## 📞 如果问题仍然存在

1. **检查服务器日志**: 查看 Prompt Tools 服务器的控制台输出
2. **检查扩展权限**: 确认 `manifest.json` 中的权限配置
3. **重启浏览器**: 完全关闭并重新打开 Chrome
4. **清除缓存**: 清除浏览器缓存和扩展数据

修复后，您应该能够正常使用右键菜单和快捷键添加提示词功能！
