// Prompt Tools Chrome Extension - Popup Script
class PromptToolsPopup {
  constructor() {
    // 使用配置文件中的API基础URL
    this.config = window.PromptToolsConfig || {};
    this.apiBase = this.config.getApiBaseUrl ? this.config.getApiBaseUrl() : 'http://localhost:18080/api';
    this.requestConfig = this.config.getRequestConfig ? this.config.getRequestConfig() : {};

    this.prompts = [];
    this.filteredPrompts = [];
    this.currentCategory = 'all';
    this.contextMenuTarget = null;
    this.currentView = 'list'; // 'list' or 'edit'
    this.editingPrompt = null;
    this.isNewPrompt = false;
    this.availableTags = []; // 从API获取的所有标签
    this.categoryButtons = []; // 动态生成的分类按钮

    console.log('弹窗脚本初始化 - 当前环境:', this.config.currentEnvironment);
    console.log('API基础URL:', this.apiBase);

    this.init();
  }

  async init() {
    this.bindEvents();
    await this.loadTags();
    await this.loadPrompts();
  }

  bindEvents() {
    // 检查必要的DOM元素是否存在
    const requiredElements = [
      'createBtn', 'refreshBtn', 'openWebBtn', 'retryBtn',
      'searchInput', 'searchBtn', 'backToListBtn', 'savePromptBtn',
      'editForm', 'editPromptContent', 'copyAndEditBtn',
      'confirmCancel', 'confirmDelete', 'confirmDialog'
    ];

    for (const elementId of requiredElements) {
      const element = document.getElementById(elementId);
      if (!element) {
        console.error(`缺少必要的DOM元素: ${elementId}`);
      }
    }

    // 创建按钮
    document.getElementById('createBtn').addEventListener('click', () => {
      this.showEditView();
    });

    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.loadPrompts();
    });

    // 打开网页版按钮
    document.getElementById('openWebBtn').addEventListener('click', () => {
      // 使用配置文件中的基础URL
      const baseUrl = this.config.getCurrentEnvironment ?
        this.config.getCurrentEnvironment().baseUrl :
        'http://localhost:18080';

      console.log('打开 Prompt Tools 网页版:', baseUrl);
      chrome.tabs.create({ url: baseUrl });
    });

    // 重试按钮
    document.getElementById('retryBtn').addEventListener('click', () => {
      this.loadPrompts();
    });

    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');

    searchInput.addEventListener('input', (e) => {
      this.filterPrompts(e.target.value);
    });

    searchBtn.addEventListener('click', () => {
      this.filterPrompts(searchInput.value);
    });

    // 回车搜索
    searchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.filterPrompts(e.target.value);
      }
    });

    // 分类按钮事件将在generateCategoryButtons中动态绑定

    // 编辑视图事件
    document.getElementById('backToListBtn').addEventListener('click', () => {
      this.showListView();
    });

    document.getElementById('savePromptBtn').addEventListener('click', () => {
      this.savePrompt();
    });

    document.getElementById('editForm').addEventListener('submit', (e) => {
      e.preventDefault();
      this.savePrompt();
    });

    // 字符计数
    document.getElementById('editPromptContent').addEventListener('input', () => {
      this.updateCharCounter();
    });

    // 右键菜单
    document.getElementById('copyAndEditBtn').addEventListener('click', () => {
      this.handleCopyAndEdit();
    });

    // 确认对话框
    document.getElementById('confirmCancel').addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('点击取消按钮');
      this.hideConfirmDialog();
    });

    document.getElementById('confirmDelete').addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('点击删除按钮');
      this.confirmDelete();
    });

    // 点击其他地方关闭右键菜单
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.context-menu')) {
        this.hideContextMenu();
      }
    });

    // 点击遮罩关闭确认对话框
    document.querySelector('.confirm-overlay').addEventListener('click', (e) => {
      e.stopPropagation();
      this.hideConfirmDialog();
    });

    // 防止确认对话框内容区域的点击事件冒泡到遮罩
    document.querySelector('.confirm-content').addEventListener('click', (e) => {
      e.stopPropagation();
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (this.currentView === 'edit') {
        // Ctrl+S 保存
        if (e.ctrlKey && e.key === 's') {
          e.preventDefault();
          this.savePrompt();
        }
        // Esc 返回
        if (e.key === 'Escape') {
          this.showListView();
        }
      }
    });

    // 自动保存草稿
    this.setupAutoSave();
  }

  async loadTags() {
    try {
      // 使用配置文件中的API端点
      const apiUrl = this.config.getApiEndpoint ?
        this.config.getApiEndpoint('tags', 'list') :
        `${this.apiBase}/prompts/meta/tags`;

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      console.log('加载标签列表:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: headers,
      });

      if (!response.ok) {
        throw new Error(`请求失败 ${response.status}: ${response.statusText}`);
      }

      this.availableTags = await response.json();
      console.log('成功加载标签:', this.availableTags);

    } catch (error) {
      console.error('加载标签失败:', error);
      // 如果加载失败，使用空数组
      this.availableTags = [];
    }
  }

  generateCategoryButtons() {
    const categoryContainer = document.querySelector('.category-buttons');
    if (!categoryContainer) return;

    // 清空现有按钮
    categoryContainer.innerHTML = '';

    // 添加"全部"按钮
    const allButton = document.createElement('button');
    allButton.className = 'category-btn active';
    allButton.setAttribute('data-category', 'all');
    allButton.textContent = '全部';
    categoryContainer.appendChild(allButton);

    // 根据标签生成分类
    const categories = this.generateCategoriesFromTags();

    categories.forEach(category => {
      const button = document.createElement('button');
      button.className = 'category-btn';
      button.setAttribute('data-category', category.key);
      button.textContent = `${category.name} (${category.count})`;
      categoryContainer.appendChild(button);
    });

    // 重新绑定分类按钮事件
    this.bindCategoryEvents();
  }

  generateCategoriesFromTags() {
    if (!this.availableTags.length || !this.prompts.length) return [];

    // 统计每个标签的使用频率
    const tagCounts = {};
    this.availableTags.forEach(tag => {
      const count = this.prompts.filter(prompt =>
        prompt.tags && prompt.tags.includes(tag)
      ).length;
      if (count > 0) {
        tagCounts[tag] = count;
      }
    });

    // 按使用频率排序，取前8个作为分类
    const sortedTags = Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 8);

    return sortedTags.map(([tag, count]) => ({
      key: tag,
      name: tag,
      count: count
    }));
  }

  bindCategoryEvents() {
    // 分类按钮事件
    document.querySelectorAll('.category-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.handleCategoryClick(e);
      });
    });
  }

  async loadPrompts() {
    this.showLoading();

    try {
      // 使用配置文件中的API端点
      const apiUrl = this.config.getApiEndpoint ?
        this.config.getApiEndpoint('prompts', 'list') :
        `${this.apiBase}/prompts`;

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      console.log('加载提示词列表:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: headers,
      });

      if (!response.ok) {
        throw new Error(`请求失败 ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      this.prompts = Array.isArray(data) ? data : [];

      // 重新生成分类按钮（基于新的提示词数据）
      this.generateCategoryButtons();

      // 应用当前的筛选条件
      this.filterPrompts(document.getElementById('searchInput').value);
      this.hideLoading();

      console.log(`成功加载 ${this.prompts.length} 个提示词`);

      // 如果没有数据，显示空状态
      if (this.prompts.length === 0) {
        this.showEmptyState();
      }

    } catch (error) {
      console.error('加载提示词失败:', error);
      this.showError();
    }
  }

  showEmptyState() {
    document.getElementById('loadingSpinner').classList.add('hidden');
    document.getElementById('errorMessage').classList.add('hidden');
    document.getElementById('promptsList').classList.add('hidden');
    document.getElementById('emptyState').classList.remove('hidden');
  }

  handleCategoryClick(e) {
    const btn = e.currentTarget;
    const category = btn.dataset.category;

    // 更新按钮状态
    document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
    btn.classList.add('active');

    this.currentCategory = category;
    this.filterPrompts(document.getElementById('searchInput').value);
  }

  filterPrompts(query) {
    let basePrompts = this.prompts;

    // 先按分类筛选
    if (this.currentCategory !== 'all') {
      basePrompts = this.prompts.filter(prompt => {
        if (!prompt.tags || !Array.isArray(prompt.tags)) return false;
        // 直接匹配标签名称
        return prompt.tags.includes(this.currentCategory);
      });
    }

    // 再按搜索词筛选
    if (!query.trim()) {
      this.filteredPrompts = [...basePrompts];
    } else {
      const searchTerm = query.toLowerCase();
      this.filteredPrompts = basePrompts.filter(prompt =>
        prompt.name.toLowerCase().includes(searchTerm) ||
        prompt.content.toLowerCase().includes(searchTerm) ||
        (prompt.tags && prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm))) ||
        (prompt.source && prompt.source.toLowerCase().includes(searchTerm))
      );
    }
    this.renderPrompts();
  }

  renderPrompts() {
    const container = document.getElementById('promptsList');
    const emptyState = document.getElementById('emptyState');

    if (this.filteredPrompts.length === 0) {
      container.classList.add('hidden');
      emptyState.classList.remove('hidden');
      return;
    }

    emptyState.classList.add('hidden');
    container.classList.remove('hidden');

    container.innerHTML = this.filteredPrompts.map(prompt => this.createPromptHTML(prompt)).join('');

    // 绑定事件
    container.querySelectorAll('.prompt-item').forEach((item, index) => {
      const prompt = this.filteredPrompts[index];

      // 右键菜单
      item.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        this.showContextMenu(e, prompt);
      });

      // 复制按钮
      const copyBtn = item.querySelector('.copy-btn');
      if (copyBtn) {
        copyBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          this.copyPrompt(prompt, item);
        });
      }

      // 编辑按钮
      const editBtn = item.querySelector('.edit-btn');
      if (editBtn) {
        editBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          this.editPrompt(prompt);
        });
      }

      // 删除按钮
      const deleteBtn = item.querySelector('.delete-btn');
      if (deleteBtn) {
        deleteBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          this.showDeleteConfirm(prompt);
        });
      }
    });
  }

  createPromptHTML(prompt) {
    const tags = prompt.tags || [];
    const tagsHTML = tags.map(tag => `<span class="tag">${this.escapeHtml(tag)}</span>`).join('');

    return `
      <div class="prompt-item" data-id="${prompt.id}">
        <div class="prompt-header">
          <div class="prompt-name">${this.escapeHtml(prompt.name)}</div>
          ${prompt.source ? `<div class="prompt-source">${this.escapeHtml(prompt.source)}</div>` : ''}
        </div>
        <div class="prompt-content">${this.escapeHtml(prompt.content)}</div>
        <div class="prompt-actions">
          <div class="prompt-tags">${tagsHTML}</div>
          <div class="action-buttons">
            <button class="action-btn copy-btn" title="复制">
              <i class="material-icons">content_copy</i>
            </button>
            <button class="action-btn edit-btn" title="编辑">
              <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" title="删除">
              <i class="material-icons">delete</i>
            </button>
          </div>
        </div>
        <div class="copy-indicator">已复制!</div>
      </div>
    `;
  }

  async copyPrompt(prompt, element) {
    try {
      await navigator.clipboard.writeText(prompt.content);

      // 显示复制成功效果
      element.classList.add('copied');
      const indicator = element.querySelector('.copy-indicator');
      indicator.classList.add('show');

      // 显示状态消息
      this.showStatus('✅ 已复制到剪贴板', 'success');

      // 重置样式
      setTimeout(() => {
        element.classList.remove('copied');
        indicator.classList.remove('show');
      }, 1500);

    } catch (error) {
      console.error('复制失败:', error);
      this.showStatus('❌ 复制失败', 'error');
    }
  }

  // 视图切换方法
  showListView() {
    document.getElementById('listView').classList.remove('hidden');
    document.getElementById('editView').classList.add('hidden');
    this.currentView = 'list';
    this.editingPrompt = null;
    this.isNewPrompt = false;

    // 清除草稿
    this.clearDraft();
  }

  showEditView(prompt = null) {
    document.getElementById('listView').classList.add('hidden');
    document.getElementById('editView').classList.remove('hidden');
    this.currentView = 'edit';

    if (prompt) {
      // 编辑现有提示词
      this.editingPrompt = prompt;
      this.isNewPrompt = false;
      document.getElementById('editTitle').textContent = '编辑提示词';
      document.getElementById('savePromptBtn').innerHTML = '<i class="material-icons">save</i><span>保存</span>';
      this.fillEditForm(prompt);
    } else {
      // 创建新提示词
      this.editingPrompt = null;
      this.isNewPrompt = true;
      document.getElementById('editTitle').textContent = '创建提示词';
      document.getElementById('savePromptBtn').innerHTML = '<i class="material-icons">add</i><span>创建</span>';
      this.clearEditForm();

      // 尝试加载草稿
      this.loadDraft();
    }

    this.updateCharCounter();
  }

  editPrompt(prompt) {
    this.showEditView(prompt);
  }

  // 表单操作方法
  fillEditForm(prompt) {
    document.getElementById('editPromptName').value = prompt.name || '';
    document.getElementById('editPromptContent').value = prompt.content || '';
    document.getElementById('editPromptTags').value = Array.isArray(prompt.tags) ? prompt.tags.join(', ') : '';
    document.getElementById('editPromptSource').value = prompt.source || '';
    document.getElementById('editPromptNotes').value = prompt.notes || '';
  }

  clearEditForm() {
    document.getElementById('editForm').reset();
  }

  updateCharCounter() {
    const content = document.getElementById('editPromptContent').value;
    const charCount = content.length;
    document.getElementById('editCharCount').textContent = charCount;
  }

  showDeleteConfirm(prompt) {
    this.pendingDeletePrompt = prompt;
    document.getElementById('confirmMessage').textContent =
      `确定要删除提示词"${prompt.name}"吗？`;
    document.getElementById('confirmDialog').classList.remove('hidden');
  }

  hideConfirmDialog() {
    document.getElementById('confirmDialog').classList.add('hidden');
    this.pendingDeletePrompt = null;
  }

  async confirmDelete() {
    console.log('confirmDelete 方法被调用');
    console.log('pendingDeletePrompt:', this.pendingDeletePrompt);

    if (!this.pendingDeletePrompt) {
      console.log('没有待删除的提示词');
      return;
    }

    try {
      const apiUrl = this.config.getApiEndpoint ?
        this.config.getApiEndpoint('prompts', 'delete', { id: this.pendingDeletePrompt.id }) :
        `${this.apiBase}/prompts/${this.pendingDeletePrompt.id}`;

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      console.log('删除提示词:', apiUrl, '提示词ID:', this.pendingDeletePrompt.id);

      const response = await fetch(apiUrl, {
        method: 'DELETE',
        headers: headers
      });

      console.log('删除响应状态:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('删除失败响应:', errorText);
        throw new Error(`删除请求失败 ${response.status}: ${response.statusText}`);
      }

      console.log('删除成功');
      this.showStatus('✅ 删除成功', 'success');
      this.hideConfirmDialog();

      // 重新加载数据
      await this.loadPrompts();

    } catch (error) {
      console.error('删除失败:', error);
      this.showStatus('❌ 删除失败: ' + error.message, 'error');
      this.hideConfirmDialog();
    }
  }

  showContextMenu(e, prompt) {
    this.contextMenuTarget = prompt;
    const menu = document.getElementById('contextMenu');

    menu.style.left = e.pageX + 'px';
    menu.style.top = e.pageY + 'px';
    menu.classList.remove('hidden');
  }

  hideContextMenu() {
    document.getElementById('contextMenu').classList.add('hidden');
    this.contextMenuTarget = null;
  }

  async savePrompt() {
    const form = document.getElementById('editForm');
    const formData = new FormData(form);

    const name = formData.get('name').trim();
    const content = formData.get('content').trim();
    const tags = formData.get('tags').trim();
    const source = formData.get('source').trim();
    const notes = formData.get('notes').trim();

    if (!name) {
      this.showStatus('请输入标题', 'error');
      return;
    }

    if (!content) {
      this.showStatus('请输入内容', 'error');
      return;
    }

    try {
      const tagsArray = tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

      const promptData = {
        name,
        content,
        tags: tagsArray,
        source: source || null,
        notes: notes || null
      };

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      let apiUrl, method;

      if (this.isNewPrompt) {
        // 创建新提示词
        apiUrl = this.config.getApiEndpoint ?
          this.config.getApiEndpoint('prompts', 'create') :
          `${this.apiBase}/prompts`;
        method = 'POST';
      } else {
        // 更新现有提示词
        apiUrl = this.config.getApiEndpoint ?
          this.config.getApiEndpoint('prompts', 'update', { id: this.editingPrompt.id }) :
          `${this.apiBase}/prompts/${this.editingPrompt.id}`;
        method = 'PUT';
        promptData.id = this.editingPrompt.id;
      }

      console.log('保存提示词:', method, apiUrl, promptData);

      const response = await fetch(apiUrl, {
        method: method,
        headers: headers,
        body: JSON.stringify(promptData)
      });

      if (!response.ok) {
        throw new Error(`保存请求失败 ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('保存成功:', result);

      // 清除草稿
      this.clearDraft();

      this.showStatus(this.isNewPrompt ? '✅ 创建成功！' : '✅ 保存成功！', 'success');

      // 重新加载数据并返回列表视图
      await this.loadPrompts();
      setTimeout(() => {
        this.showListView();
      }, 1000);

    } catch (error) {
      console.error('保存失败:', error);
      this.showStatus('❌ 保存失败: ' + error.message, 'error');
    }
  }

  async handleCopyAndEdit() {
    if (!this.contextMenuTarget) return;

    try {
      // 复制内容到剪贴板
      await navigator.clipboard.writeText(this.contextMenuTarget.content);

      // 显示编辑视图并预填充内容
      this.showEditView();
      document.getElementById('editPromptContent').value = this.contextMenuTarget.content;
      this.updateCharCounter();

      this.hideContextMenu();
      this.showStatus('✅ 已复制内容，请填写标题和标签', 'success');

    } catch (error) {
      console.error('复制并编辑失败:', error);
      this.showStatus('❌ 操作失败', 'error');
    }
  }

  // 草稿管理
  setupAutoSave() {
    // 每30秒自动保存到本地存储作为草稿
    setInterval(() => {
      if (this.currentView === 'edit') {
        this.saveDraft();
      }
    }, 30000);
  }

  saveDraft() {
    if (this.currentView !== 'edit' || !this.isNewPrompt) return;

    const formData = {
      name: document.getElementById('editPromptName').value,
      content: document.getElementById('editPromptContent').value,
      tags: document.getElementById('editPromptTags').value,
      source: document.getElementById('editPromptSource').value,
      notes: document.getElementById('editPromptNotes').value,
      timestamp: Date.now()
    };

    localStorage.setItem('prompttools_draft', JSON.stringify(formData));
  }

  loadDraft() {
    const draft = localStorage.getItem('prompttools_draft');
    if (draft && this.isNewPrompt) {
      try {
        const data = JSON.parse(draft);
        // 只在草稿不超过1小时的情况下加载
        if (Date.now() - data.timestamp < 3600000) {
          if (confirm('发现未保存的草稿，是否恢复？')) {
            this.fillEditForm(data);
            this.updateCharCounter();
          }
        }
      } catch (error) {
        console.error('加载草稿失败:', error);
      }
    }
  }

  clearDraft() {
    localStorage.removeItem('prompttools_draft');
  }

  showLoading() {
    document.getElementById('loadingSpinner').classList.remove('hidden');
    document.getElementById('errorMessage').classList.add('hidden');
    document.getElementById('promptsList').classList.add('hidden');
    document.getElementById('emptyState').classList.add('hidden');
  }

  hideLoading() {
    document.getElementById('loadingSpinner').classList.add('hidden');
  }

  showError() {
    document.getElementById('loadingSpinner').classList.add('hidden');
    document.getElementById('errorMessage').classList.remove('hidden');
    document.getElementById('promptsList').classList.add('hidden');
    document.getElementById('emptyState').classList.add('hidden');
  }

  showStatus(message, type = 'success') {
    const statusEl = document.getElementById('statusMessage');
    statusEl.textContent = message;
    statusEl.className = `status-message ${type}`;
    statusEl.classList.remove('hidden');
    
    setTimeout(() => {
      statusEl.classList.add('hidden');
    }, 3000);
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
  new PromptToolsPopup();
});
