// Prompt Tools Chrome Extension - 配置示例文件
// 此文件包含常用的配置示例，可以复制到 server-config.js 中使用

/* 
===========================================
快速配置示例 - 复制以下代码到 server-config.js
===========================================
*/

// 示例1: 本地开发环境（默认配置）
const EXAMPLE_LOCAL_CONFIG = {
  CURRENT_ENVIRONMENT: 'development',
  SERVER_ENVIRONMENTS: {
    development: {
      name: '本地开发',
      baseUrl: 'http://localhost:18080',
      apiPath: '/api',
      timeout: 10000,
      retryAttempts: 3,
      description: '本地开发服务器'
    }
  }
};

// 示例2: 远程服务器配置
const EXAMPLE_REMOTE_CONFIG = {
  CURRENT_ENVIRONMENT: 'custom',
  SERVER_ENVIRONMENTS: {
    custom: {
      name: '远程服务器',
      baseUrl: 'http://*************:8080', // 修改为您的服务器IP
      apiPath: '/api',
      timeout: 15000,
      retryAttempts: 2,
      description: '远程开发服务器'
    }
  }
};

// 示例3: 生产环境配置
const EXAMPLE_PRODUCTION_CONFIG = {
  CURRENT_ENVIRONMENT: 'production',
  SERVER_ENVIRONMENTS: {
    production: {
      name: '生产环境',
      baseUrl: 'https://api.prompttools.com', // 使用HTTPS
      apiPath: '/api/v1',
      timeout: 20000,
      retryAttempts: 3,
      description: '生产环境服务器'
    }
  }
};

// 示例4: 多环境完整配置
const EXAMPLE_MULTI_ENV_CONFIG = {
  CURRENT_ENVIRONMENT: 'development', // 当前使用的环境
  
  SERVER_ENVIRONMENTS: {
    // 本地开发
    development: {
      name: '本地开发',
      baseUrl: 'http://localhost:18080',
      apiPath: '/api',
      timeout: 10000,
      retryAttempts: 3,
      description: '本地开发服务器'
    },
    
    // 测试环境
    testing: {
      name: '测试环境',
      baseUrl: 'http://test.company.com:8080',
      apiPath: '/api',
      timeout: 15000,
      retryAttempts: 2,
      description: '团队测试服务器'
    },
    
    // 预发布环境
    staging: {
      name: '预发布环境',
      baseUrl: 'https://staging.prompttools.com',
      apiPath: '/api/v1',
      timeout: 18000,
      retryAttempts: 3,
      description: '预发布环境'
    },
    
    // 生产环境
    production: {
      name: '生产环境',
      baseUrl: 'https://api.prompttools.com',
      apiPath: '/api/v1',
      timeout: 20000,
      retryAttempts: 3,
      description: '生产环境服务器'
    }
  }
};

// 示例5: 自定义扩展功能配置
const EXAMPLE_EXTENSION_CONFIG = {
  // 通知设置
  notifications: {
    enabled: true,              // 启用通知
    duration: 3000,             // 3秒后自动隐藏
    position: 'top-right'       // 右上角显示
  },

  // 快捷键设置
  shortcuts: {
    quickAdd: 'Ctrl+Shift+P',   // 快速添加
    openExtension: 'Ctrl+Shift+O' // 打开扩展
  },

  // 界面设置
  ui: {
    theme: 'light',             // 浅色主题
    language: 'zh-CN',          // 中文界面
    maxPromptPreview: 100       // 预览最多100字符
  },

  // 数据设置
  data: {
    cacheEnabled: true,         // 启用缓存
    cacheExpiry: 300000,        // 5分钟缓存
    autoSync: true              // 自动同步
  }
};

// 示例6: 禁用通知的配置
const EXAMPLE_NO_NOTIFICATION_CONFIG = {
  notifications: {
    enabled: false,             // 禁用所有通知
    duration: 0,
    position: 'none'
  }
};

// 示例7: 自定义快捷键配置
const EXAMPLE_CUSTOM_SHORTCUTS_CONFIG = {
  shortcuts: {
    quickAdd: 'Ctrl+Alt+P',     // 使用 Ctrl+Alt+P
    openExtension: 'Ctrl+Alt+O' // 使用 Ctrl+Alt+O
  }
};

/*
===========================================
使用方法：
===========================================

1. 选择合适的配置示例
2. 复制对应的配置代码
3. 粘贴到 server-config.js 文件中
4. 根据实际情况修改服务器地址和端口
5. 重新加载Chrome扩展

===========================================
常用配置场景：
===========================================

场景1: 本地开发
- 使用 EXAMPLE_LOCAL_CONFIG
- 确保本地服务器运行在 localhost:18080

场景2: 团队协作
- 使用 EXAMPLE_REMOTE_CONFIG
- 修改 baseUrl 为团队服务器地址

场景3: 生产部署
- 使用 EXAMPLE_PRODUCTION_CONFIG
- 确保使用 HTTPS 协议

场景4: 多环境切换
- 使用 EXAMPLE_MULTI_ENV_CONFIG
- 通过修改 CURRENT_ENVIRONMENT 快速切换

===========================================
故障排除：
===========================================

问题1: 无法连接服务器
解决: 检查 baseUrl 和端口是否正确

问题2: API调用失败
解决: 验证 apiPath 是否匹配服务器路径

问题3: 扩展无响应
解决: 重新加载扩展，检查控制台错误

问题4: 通知不显示
解决: 检查 notifications.enabled 设置

===========================================
*/

// 导出示例配置（仅用于参考，不会影响实际配置）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    EXAMPLE_LOCAL_CONFIG,
    EXAMPLE_REMOTE_CONFIG,
    EXAMPLE_PRODUCTION_CONFIG,
    EXAMPLE_MULTI_ENV_CONFIG,
    EXAMPLE_EXTENSION_CONFIG,
    EXAMPLE_NO_NOTIFICATION_CONFIG,
    EXAMPLE_CUSTOM_SHORTCUTS_CONFIG
  };
}
