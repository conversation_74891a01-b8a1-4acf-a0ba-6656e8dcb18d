# Service Worker 注册失败修复报告

## 🚨 问题描述

Chrome扩展出现Service Worker注册失败错误：
```
Service worker registration failed. Status code: 15
```

错误代码15通常表示Service Worker脚本存在语法错误或兼容性问题。

## 🔍 问题分析

经过排查，发现以下几个关键问题：

### 1. 权限缺失
- manifest.json中缺少`notifications`权限
- background.js中使用了chrome.notifications API但没有声明权限

### 2. Service Worker兼容性问题
- background.js中使用了`window`对象，但Service Worker中没有window对象
- 应该使用`self`对象替代`window`

### 3. 脚本导入问题
- hot-reload.js中包含了不兼容Service Worker的代码
- 使用了`window.location`等浏览器专用API

### 4. 命名不一致
- 代码中仍使用旧的"Prompt Tools"名称
- 需要更新为新的"Prompt Hub"名称

## ✅ 修复方案

### 修复1：添加notifications权限

**文件：** `manifest.json`
```json
"permissions": [
  "activeTab",
  "contextMenus", 
  "storage",
  "clipboardWrite",
  "management",
  "notifications"  // 新增
],
```

### 修复2：Service Worker兼容性修复

**文件：** `background.js`
```javascript
// 修复前
this.config = window.PromptToolsConfig || {};

// 修复后  
this.config = self.PromptToolsConfig || {};
```

### 修复3：安全的脚本导入

**文件：** `background.js`
```javascript
// 修复前
importScripts('server-config.js');
importScripts('hot-reload.js');

// 修复后
try {
  importScripts('server-config.js');
  // 在Service Worker中禁用热重载以避免兼容性问题
  // importScripts('hot-reload.js');
} catch (error) {
  console.log('导入脚本失败，使用默认配置:', error);
}
```

### 修复4：热重载脚本兼容性

**文件：** `hot-reload.js`
```javascript
// 修复前
return chrome.runtime.getManifest().version.includes('dev') || 
       window.location.hostname === 'localhost' ||
       chrome.runtime.id === chrome.runtime.getManifest().key;

// 修复后
try {
  return chrome.runtime.getManifest().version.includes('dev') || 
         (typeof window !== 'undefined' && window.location.hostname === 'localhost') ||
         chrome.runtime.id === chrome.runtime.getManifest().key;
} catch (error) {
  // 在Service Worker中，简单返回false禁用热重载
  return false;
}
```

### 修复5：更新品牌名称

**文件：** `background.js`
- 类名：`PromptToolsBackground` → `PromptHubBackground`
- 菜单ID：`addToPromptTools` → `addToPromptHub`
- 菜单标题：`添加到 Prompt Tools` → `添加到 Prompt Hub`
- 通知标题：`Prompt Tools` → `Prompt Hub`

## 🔧 技术要点

### Service Worker vs Content Script

| 特性 | Service Worker | Content Script |
|------|----------------|----------------|
| 全局对象 | `self` | `window` |
| DOM访问 | ❌ 无法访问 | ✅ 可以访问 |
| 生命周期 | 事件驱动 | 页面绑定 |
| 权限要求 | 需要在manifest中声明 | 继承页面权限 |

### 关键修复点

1. **全局对象替换**：`window` → `self`
2. **错误处理**：添加try-catch保护脚本导入
3. **权限声明**：确保所有使用的API都有对应权限
4. **兼容性检查**：在使用浏览器专用API前检查可用性

## 🧪 验证方法

### 1. 检查Service Worker状态
```javascript
// 在Chrome开发者工具中
chrome://extensions/ → 开发者模式 → 检查视图：Service Worker
```

### 2. 查看控制台日志
```javascript
// 应该看到以下日志
"后台脚本初始化 - 当前环境: development"
"API基础URL: http://localhost:18080/api"
```

### 3. 测试功能
- ✅ 右键菜单显示正确
- ✅ 通知功能正常
- ✅ 扩展图标可点击
- ✅ 无控制台错误

## 📋 修复清单

- [x] 添加notifications权限到manifest.json
- [x] 修复background.js中的window对象引用
- [x] 添加安全的脚本导入机制
- [x] 修复hot-reload.js的Service Worker兼容性
- [x] 更新所有"Prompt Tools"为"Prompt Hub"
- [x] 更新类名和方法名
- [x] 更新右键菜单配置
- [x] 更新通知消息

## 🎯 预期结果

修复后，Chrome扩展应该能够：

1. **正常加载**：Service Worker成功注册，无错误代码15
2. **功能完整**：所有功能正常工作，包括右键菜单、通知等
3. **品牌一致**：所有界面元素显示"Prompt Hub"
4. **稳定运行**：无控制台错误，性能稳定

## 🔄 后续优化建议

1. **分离配置**：考虑为Service Worker创建专用的配置文件
2. **错误监控**：添加更完善的错误监控和上报机制
3. **性能优化**：优化Service Worker的启动和响应时间
4. **测试覆盖**：添加自动化测试确保Service Worker功能正常

## 📝 注意事项

1. **重新加载扩展**：修改manifest.json后需要重新加载扩展
2. **清除缓存**：可能需要清除浏览器缓存以确保更新生效
3. **权限确认**：新增权限可能需要用户重新确认
4. **兼容性测试**：在不同Chrome版本中测试确保兼容性

通过以上修复，Service Worker注册失败的问题应该得到完全解决。
