#!/usr/bin/env node

// Chrome Extension 开发环境启动脚本
// 同时启动主应用服务器和扩展热重载服务器

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class DevEnvironment {
  constructor() {
    this.processes = [];
    this.isShuttingDown = false;
    
    console.log('🚀 启动 Chrome Extension 开发环境');
  }

  async start() {
    try {
      // 检查依赖
      await this.checkDependencies();
      
      // 启动主应用服务器
      this.startMainServer();
      
      // 启动扩展热重载服务器
      this.startHotReloadServer();
      
      // 设置优雅关闭
      this.setupGracefulShutdown();
      
      // 显示开发指南
      this.showDevelopmentGuide();
      
    } catch (error) {
      console.error('❌ 启动失败:', error.message);
      process.exit(1);
    }
  }

  async checkDependencies() {
    console.log('🔍 检查依赖...');
    
    // 检查主项目的 package.json
    const mainPackageJson = path.join(__dirname, '..', 'package.json');
    if (!fs.existsSync(mainPackageJson)) {
      throw new Error('未找到主项目的 package.json 文件');
    }
    
    // 检查 node_modules
    const nodeModules = path.join(__dirname, '..', 'node_modules');
    if (!fs.existsSync(nodeModules)) {
      console.log('📦 安装依赖...');
      await this.runCommand('npm', ['install'], { cwd: path.join(__dirname, '..') });
    }
    
    console.log('✅ 依赖检查完成');
  }

  startMainServer() {
    console.log('🌐 启动主应用服务器...');
    
    const mainServerProcess = spawn('npm', ['run', 'dev'], {
      cwd: path.join(__dirname, '..'),
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true
    });

    mainServerProcess.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('localhost') || output.includes('服务器') || output.includes('Server')) {
        console.log('🌐 主服务器:', output.trim());
      }
    });

    mainServerProcess.stderr.on('data', (data) => {
      const error = data.toString();
      if (!error.includes('ExperimentalWarning')) {
        console.error('🌐 主服务器错误:', error.trim());
      }
    });

    mainServerProcess.on('close', (code) => {
      if (!this.isShuttingDown) {
        console.log(`🌐 主服务器进程退出，代码: ${code}`);
      }
    });

    this.processes.push({
      name: '主应用服务器',
      process: mainServerProcess
    });
  }

  startHotReloadServer() {
    console.log('🔥 启动扩展热重载服务器...');
    
    const hotReloadProcess = spawn('node', ['dev-server.js'], {
      cwd: __dirname,
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true
    });

    hotReloadProcess.stdout.on('data', (data) => {
      console.log('🔥 热重载:', data.toString().trim());
    });

    hotReloadProcess.stderr.on('data', (data) => {
      console.error('🔥 热重载错误:', data.toString().trim());
    });

    hotReloadProcess.on('close', (code) => {
      if (!this.isShuttingDown) {
        console.log(`🔥 热重载服务器进程退出，代码: ${code}`);
      }
    });

    this.processes.push({
      name: '热重载服务器',
      process: hotReloadProcess
    });
  }

  setupGracefulShutdown() {
    const shutdown = () => {
      if (this.isShuttingDown) return;
      this.isShuttingDown = true;
      
      console.log('\n🛑 正在关闭开发环境...');
      
      this.processes.forEach(({ name, process }) => {
        console.log(`🛑 关闭 ${name}...`);
        process.kill('SIGTERM');
      });
      
      setTimeout(() => {
        console.log('✅ 开发环境已关闭');
        process.exit(0);
      }, 2000);
    };

    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
  }

  showDevelopmentGuide() {
    setTimeout(() => {
      console.log('\n' + '='.repeat(60));
      console.log('🎉 Chrome Extension 开发环境已启动！');
      console.log('='.repeat(60));
      console.log('');
      console.log('📋 服务状态:');
      console.log('   🌐 主应用服务器: http://localhost:18080');
      console.log('   🔥 热重载服务器: http://localhost:8080');
      console.log('');
      console.log('🔧 Chrome 扩展安装步骤:');
      console.log('   1. 打开 Chrome 浏览器');
      console.log('   2. 访问 chrome://extensions/');
      console.log('   3. 开启右上角的"开发者模式"');
      console.log('   4. 点击"加载已解压的扩展程序"');
      console.log('   5. 选择 chrome-extension 文件夹');
      console.log('');
      console.log('🔥 热更新功能:');
      console.log('   ✅ 修改任何扩展文件后会自动重载');
      console.log('   ✅ 无需手动刷新扩展');
      console.log('   ✅ 实时查看代码变化效果');
      console.log('');
      console.log('🛠️ 调试方法:');
      console.log('   📱 弹窗调试: 右键扩展图标 → "检查弹出式窗口"');
      console.log('   🔧 后台脚本: 扩展管理页面 → 点击"service worker"');
      console.log('   🌐 内容脚本: 网页 F12 → Console 标签');
      console.log('');
      console.log('⚡ 快捷操作:');
      console.log('   🔄 手动重载: curl http://localhost:8080/reload');
      console.log('   📊 服务状态: curl http://localhost:8080/status');
      console.log('');
      console.log('🛑 停止开发环境: Ctrl+C');
      console.log('='.repeat(60));
    }, 3000);
  }

  runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, { ...options, stdio: 'inherit' });
      
      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`命令执行失败: ${command} ${args.join(' ')}`));
        }
      });
    });
  }
}

// 启动开发环境
if (require.main === module) {
  const devEnv = new DevEnvironment();
  devEnv.start().catch(console.error);
}

module.exports = DevEnvironment;
