<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chrome扩展修复测试</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .test-section {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 5px;
    }
    .test-title {
      color: #1976d2;
      margin-bottom: 10px;
      font-weight: bold;
    }
    .test-result {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .success {
      background-color: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #4caf50;
    }
    .error {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #f44336;
    }
    .info {
      background-color: #e3f2fd;
      color: #1565c0;
      border: 1px solid #2196f3;
    }
    button {
      background-color: #1976d2;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background-color: #1565c0;
    }
    .code {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      font-family: 'Consolas', monospace;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔧 Chrome扩展修复测试</h1>
    
    <div class="test-section">
      <div class="test-title">📋 修复内容总结</div>
      <div class="test-result info">
        <strong>已修复的问题：</strong>
        <ul>
          <li>✅ 修复了server-config.js中标签API端点配置错误</li>
          <li>✅ 修复了popup.js中getApiEndpoint参数传递错误</li>
          <li>✅ 将所有错误消息改为中文显示</li>
          <li>✅ 确保所有用户面向的消息都是中文</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <div class="test-title">🔍 配置验证</div>
      <button onclick="testConfig()">测试配置文件</button>
      <div id="configResult" class="test-result" style="display:none;"></div>
    </div>

    <div class="test-section">
      <div class="test-title">🌐 API端点测试</div>
      <button onclick="testApiEndpoints()">测试API端点</button>
      <div id="apiResult" class="test-result" style="display:none;"></div>
    </div>

    <div class="test-section">
      <div class="test-title">🔗 服务器连接测试</div>
      <button onclick="testServerConnection()">测试服务器连接</button>
      <div id="connectionResult" class="test-result" style="display:none;"></div>
    </div>

    <div class="test-section">
      <div class="test-title">📝 修复详情</div>
      <div class="test-result info">
        <h4>1. 配置文件修复 (server-config.js)</h4>
        <div class="code">
          // 修复前：<br>
          tags: { list: '/tags' }<br><br>
          // 修复后：<br>
          tags: { list: '/prompts/meta/tags' }
        </div>

        <h4>2. API调用修复 (popup.js)</h4>
        <div class="code">
          // 修复前：<br>
          getApiEndpoint('prompts', 'delete', this.pendingDeletePrompt.id)<br><br>
          // 修复后：<br>
          getApiEndpoint('prompts', 'delete', { id: this.pendingDeletePrompt.id })
        </div>

        <h4>3. 错误消息中文化</h4>
        <div class="code">
          // 修复前：<br>
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)<br><br>
          // 修复后：<br>
          throw new Error(`请求失败 ${response.status}: ${response.statusText}`)
        </div>
      </div>
    </div>
  </div>

  <script src="server-config.js"></script>
  <script>
    function testConfig() {
      const result = document.getElementById('configResult');
      result.style.display = 'block';
      
      try {
        // 测试配置是否正确加载
        if (typeof window.PromptToolsConfig === 'undefined') {
          throw new Error('配置文件未正确加载');
        }
        
        const config = window.PromptToolsConfig;
        const apiBase = config.getApiBaseUrl();
        const tagsEndpoint = config.getApiEndpoint('tags', 'list');
        
        result.className = 'test-result success';
        result.innerHTML = `
          <strong>✅ 配置测试通过</strong><br>
          API基础URL: ${apiBase}<br>
          标签端点: ${tagsEndpoint}<br>
          当前环境: ${config.currentEnvironment}
        `;
      } catch (error) {
        result.className = 'test-result error';
        result.innerHTML = `<strong>❌ 配置测试失败:</strong> ${error.message}`;
      }
    }

    function testApiEndpoints() {
      const result = document.getElementById('apiResult');
      result.style.display = 'block';
      
      try {
        const config = window.PromptToolsConfig;
        const endpoints = {
          '标签列表': config.getApiEndpoint('tags', 'list'),
          '提示词列表': config.getApiEndpoint('prompts', 'list'),
          '创建提示词': config.getApiEndpoint('prompts', 'create'),
          '更新提示词': config.getApiEndpoint('prompts', 'update', { id: 123 }),
          '删除提示词': config.getApiEndpoint('prompts', 'delete', { id: 123 })
        };
        
        let html = '<strong>✅ API端点测试通过</strong><br>';
        for (const [name, url] of Object.entries(endpoints)) {
          html += `${name}: ${url}<br>`;
        }
        
        result.className = 'test-result success';
        result.innerHTML = html;
      } catch (error) {
        result.className = 'test-result error';
        result.innerHTML = `<strong>❌ API端点测试失败:</strong> ${error.message}`;
      }
    }

    async function testServerConnection() {
      const result = document.getElementById('connectionResult');
      result.style.display = 'block';
      result.className = 'test-result info';
      result.innerHTML = '<strong>🔄 正在测试服务器连接...</strong>';
      
      try {
        const config = window.PromptToolsConfig;
        const isConnected = await config.validateServerConnection();
        
        if (isConnected) {
          result.className = 'test-result success';
          result.innerHTML = '<strong>✅ 服务器连接成功</strong><br>Prompt Tools 服务器运行正常';
        } else {
          result.className = 'test-result error';
          result.innerHTML = '<strong>❌ 服务器连接失败</strong><br>请确保 Prompt Tools 服务器正在运行 (http://localhost:18080)';
        }
      } catch (error) {
        result.className = 'test-result error';
        result.innerHTML = `<strong>❌ 连接测试失败:</strong> ${error.message}`;
      }
    }

    // 页面加载时自动测试配置
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🔧 Chrome扩展修复测试页面已加载');
      testConfig();
    });
  </script>
</body>
</html>
