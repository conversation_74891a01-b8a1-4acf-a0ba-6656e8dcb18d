@echo off
chcp 65001 >nul
echo 🚀 启动 Chrome Extension 开发环境
echo.

echo 📁 当前目录: %cd%
echo 🔍 检查 Node.js...

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
echo.

echo 🌐 启动开发服务器...
start "Chrome Extension Dev Server" cmd /k "node start-dev.js"

echo.
echo ✅ 开发环境启动完成！
echo.
echo 📋 接下来的步骤:
echo    1. 打开 Chrome 浏览器
echo    2. 访问 chrome://extensions/
echo    3. 开启"开发者模式"
echo    4. 点击"加载已解压的扩展程序"
echo    5. 选择当前文件夹 (chrome-extension)
echo.
echo 🔥 热更新已启用，修改代码后扩展会自动重载
echo.
pause
