# Firefox 扩展安装指南

## 📋 安装前准备

### 1. 确保服务器运行
在安装扩展之前，请确保 Prompt Tools 服务器正在运行：

```bash
# 在项目根目录执行
cd e:\Project\Prompt-Tools-main
npm start
# 或
node server/src/app.js
```

服务器应该运行在 `http://localhost:18080`

### 2. 检查扩展文件
确保 `firefox-extension` 目录包含以下文件：
- `manifest.json` - 扩展配置文件
- `background.js` - 背景脚本
- `content.js` - 内容脚本
- `popup.html` - 弹窗页面
- `popup.css` - 样式文件
- `popup.js` - 弹窗脚本
- `server-config.js` - 服务器配置
- `icons/` - 图标文件夹

## 🚀 安装步骤

### 方法一：临时加载（推荐用于开发测试）

1. **打开 Firefox 浏览器**

2. **进入调试页面**
   - 在地址栏输入：`about:debugging`
   - 按回车键进入调试页面

3. **选择"此 Firefox"**
   - 点击左侧菜单中的"此 Firefox"

4. **临时载入附加组件**
   - 点击"临时载入附加组件"按钮
   - 在文件选择器中导航到 `firefox-extension` 文件夹
   - 选择 `manifest.json` 文件
   - 点击"打开"

5. **确认安装**
   - 扩展应该出现在已安装的附加组件列表中
   - 浏览器工具栏应该显示 Prompt Hub 图标

### 方法二：打包安装（用于正式部署）

1. **创建 XPI 文件**
   ```bash
   # 进入扩展目录
   cd firefox-extension
   
   # 创建 ZIP 文件（重命名为 .xpi）
   # Windows PowerShell:
   Compress-Archive -Path * -DestinationPath prompt-hub-firefox.xpi
   
   # 或使用 7-Zip 等工具创建 ZIP 文件，然后重命名为 .xpi
   ```

2. **安装 XPI 文件**
   - 在 Firefox 中按 `Ctrl+Shift+A` 打开附加组件管理器
   - 点击齿轮图标，选择"从文件安装附加组件"
   - 选择创建的 `.xpi` 文件
   - 确认安装

## ✅ 验证安装

### 1. 检查扩展图标
- 浏览器工具栏应该显示 Prompt Hub 图标
- 图标应该是蓝色的圆形设计

### 2. 测试弹窗功能
1. 点击 Prompt Hub 图标
2. 应该看到提示词列表弹窗
3. 如果服务器运行正常，应该能看到提示词列表
4. 如果服务器未运行，会显示连接错误信息

### 3. 测试右键菜单
1. 在任意网页上选中一段文本
2. 右键点击选中的文本
3. 应该看到"添加到 Prompt Hub"选项
4. 点击该选项应该能成功添加提示词

### 4. 测试快捷键
1. 在网页上选中文本
2. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
3. 应该看到成功添加的提示

## 🔧 配置设置

### 服务器配置
如果需要修改服务器地址，编辑 `server-config.js` 文件：

```javascript
// 修改当前环境
const CURRENT_ENVIRONMENT = 'development'; // 或 'testing', 'production', 'custom'

// 修改自定义服务器地址
SERVER_ENVIRONMENTS.custom.baseUrl = 'http://your-server:port';
```

### 重新加载扩展
修改配置后需要重新加载扩展：
1. 访问 `about:debugging`
2. 找到 Prompt Hub 扩展
3. 点击"重新载入"按钮

## 🐛 故障排除

### 扩展无法加载
**问题**：点击"临时载入附加组件"后没有反应
**解决方案**：
1. 检查 `manifest.json` 文件语法是否正确
2. 确保选择的是 `manifest.json` 文件而不是文件夹
3. 查看浏览器控制台是否有错误信息

### 无法连接服务器
**问题**：弹窗显示"连接失败"
**解决方案**：
1. 确认 Prompt Tools 服务器正在运行
2. 检查服务器地址配置是否正确
3. 确认防火墙没有阻止连接

### 右键菜单不显示
**问题**：选中文本后右键没有看到菜单选项
**解决方案**：
1. 确认扩展已正确安装并启用
2. 检查是否在支持的网页上（http/https）
3. 重新加载扩展

### 快捷键不工作
**问题**：按快捷键没有反应
**解决方案**：
1. 确认快捷键没有与其他扩展冲突
2. 检查是否在可编辑的输入框中
3. 尝试在不同的网页上测试

### 复制功能失败
**问题**：点击提示词后显示"复制失败"
**解决方案**：
1. **检查权限**：确认扩展具有 `clipboardWrite` 权限
2. **测试复制功能**：打开 `test-copy-function.html` 测试页面
3. **手动复制**：如果自动复制失败，会显示手动复制对话框
4. **浏览器设置**：检查Firefox的剪贴板访问设置
5. **重新加载扩展**：在 about:debugging 中重新加载扩展

**详细测试步骤**：
```
1. 打开 firefox-extension/test-copy-function.html
2. 点击"测试组合方法"按钮
3. 观察测试结果
4. 在粘贴区域按 Ctrl+V 验证
```

## 📱 使用技巧

### 1. 快速访问
- 将 Prompt Hub 图标固定到工具栏
- 使用快捷键快速添加选中文本

### 2. 批量管理
- 点击"打开网页版"按钮进入完整管理界面
- 在网页版中可以进行批量操作

### 3. 搜索优化
- 使用标签进行分类搜索
- 支持模糊搜索，可以搜索部分关键词

## 🔄 更新扩展

### 临时加载的扩展
1. 下载新版本文件
2. 在 `about:debugging` 中点击"重新载入"

### 已安装的扩展
1. 卸载旧版本
2. 安装新版本的 XPI 文件

## 📞 技术支持

如果遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查扩展的调试信息
3. 确认服务器日志
4. 参考项目文档或提交问题报告
