#!/usr/bin/env node

// Chrome Extension 开发服务器
// 提供热重载功能，监听文件变化并通知扩展重新加载

const fs = require('fs');
const path = require('path');
const http = require('http');
const { URL } = require('url');

class ExtensionDevServer {
  constructor(options = {}) {
    this.port = options.port || 8080;
    this.watchDir = options.watchDir || __dirname;
    this.excludePatterns = options.excludePatterns || [
      /node_modules/,
      /\.git/,
      /\.DS_Store/,
      /dev-server\.js$/,
      /package-lock\.json$/
    ];
    
    this.lastModified = Date.now();
    this.watchers = new Map();
    this.server = null;
    
    console.log('🚀 Chrome Extension 开发服务器初始化');
    console.log(`📁 监听目录: ${this.watchDir}`);
    console.log(`🌐 服务器端口: ${this.port}`);
  }

  start() {
    this.createServer();
    this.startWatching();
    
    console.log(`\n✅ 开发服务器已启动`);
    console.log(`🔗 热重载服务: http://localhost:${this.port}`);
    console.log(`📝 状态检查: http://localhost:${this.port}/status`);
    console.log(`\n💡 使用说明:`);
    console.log(`   1. 在 Chrome 中加载扩展: chrome://extensions/`);
    console.log(`   2. 开启开发者模式`);
    console.log(`   3. 点击"加载已解压的扩展程序"`);
    console.log(`   4. 选择 chrome-extension 文件夹`);
    console.log(`   5. 修改代码后扩展将自动重载 🔥\n`);
  }

  createServer() {
    this.server = http.createServer((req, res) => {
      const url = new URL(req.url, `http://localhost:${this.port}`);
      
      // 设置 CORS 头
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
      res.setHeader('Content-Type', 'application/json');

      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      switch (url.pathname) {
        case '/status':
          this.handleStatus(req, res);
          break;
        case '/reload':
          this.handleReload(req, res);
          break;
        case '/health':
          this.handleHealth(req, res);
          break;
        default:
          this.handleNotFound(req, res);
      }
    });

    this.server.listen(this.port, () => {
      console.log(`🌐 开发服务器运行在 http://localhost:${this.port}`);
    });

    this.server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${this.port} 已被占用，请尝试其他端口`);
        process.exit(1);
      } else {
        console.error('服务器错误:', error);
      }
    });
  }

  handleStatus(req, res) {
    const status = {
      status: 'running',
      lastModified: this.lastModified,
      timestamp: Date.now(),
      watchDir: this.watchDir,
      port: this.port
    };
    
    res.writeHead(200);
    res.end(JSON.stringify(status, null, 2));
  }

  handleReload(req, res) {
    this.lastModified = Date.now();
    console.log('🔄 手动触发重载');
    
    res.writeHead(200);
    res.end(JSON.stringify({ 
      success: true, 
      message: '重载信号已发送',
      timestamp: this.lastModified 
    }));
  }

  handleHealth(req, res) {
    res.writeHead(200);
    res.end(JSON.stringify({ 
      status: 'healthy',
      uptime: process.uptime(),
      memory: process.memoryUsage()
    }));
  }

  handleNotFound(req, res) {
    res.writeHead(404);
    res.end(JSON.stringify({ 
      error: 'Not Found',
      availableEndpoints: ['/status', '/reload', '/health']
    }));
  }

  startWatching() {
    console.log('👀 开始监听文件变化...');
    this.watchDirectory(this.watchDir);
  }

  watchDirectory(dir) {
    try {
      const watcher = fs.watch(dir, { recursive: true }, (eventType, filename) => {
        if (filename && this.shouldWatch(filename)) {
          const filePath = path.join(dir, filename);
          console.log(`📝 文件变化: ${eventType} - ${filename}`);
          this.onFileChange(filePath);
        }
      });

      this.watchers.set(dir, watcher);
      
      watcher.on('error', (error) => {
        console.error(`监听目录失败 ${dir}:`, error);
      });

    } catch (error) {
      console.error(`无法监听目录 ${dir}:`, error);
    }
  }

  shouldWatch(filename) {
    // 检查文件是否应该被监听
    return !this.excludePatterns.some(pattern => pattern.test(filename));
  }

  onFileChange(filePath) {
    // 防抖：避免短时间内多次触发
    clearTimeout(this.debounceTimer);
    this.debounceTimer = setTimeout(() => {
      this.lastModified = Date.now();
      console.log(`🔥 检测到变化，更新时间戳: ${new Date(this.lastModified).toLocaleTimeString()}`);
    }, 100);
  }

  stop() {
    console.log('🛑 停止开发服务器...');
    
    // 关闭文件监听器
    for (const [dir, watcher] of this.watchers) {
      watcher.close();
      console.log(`📁 停止监听: ${dir}`);
    }
    this.watchers.clear();

    // 关闭HTTP服务器
    if (this.server) {
      this.server.close(() => {
        console.log('✅ 开发服务器已停止');
      });
    }
  }
}

// 命令行启动
if (require.main === module) {
  const server = new ExtensionDevServer({
    port: process.env.PORT || 8080,
    watchDir: __dirname
  });

  server.start();

  // 优雅关闭
  process.on('SIGINT', () => {
    console.log('\n🛑 收到停止信号...');
    server.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 收到终止信号...');
    server.stop();
    process.exit(0);
  });
}

module.exports = ExtensionDevServer;
