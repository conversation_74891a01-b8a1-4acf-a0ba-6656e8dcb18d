// Prompt Hub Chrome Extension - Background Script

// 导入服务器配置和热重载脚本
try {
  importScripts('server-config.js');
  // 在Service Worker中禁用热重载以避免兼容性问题
  // importScripts('hot-reload.js');
} catch (error) {
  console.log('导入脚本失败，使用默认配置:', error);
}

class PromptHubBackground {
  constructor() {
    // Service Worker中使用self而不是window
    this.config = self.PromptToolsConfig || {};
    this.apiBase = this.config.getApiBaseUrl ? this.config.getApiBaseUrl() : 'http://localhost:18080/api';
    this.requestConfig = this.config.getRequestConfig ? this.config.getRequestConfig() : {};

    console.log('后台脚本初始化 - 当前环境:', this.config.currentEnvironment);
    console.log('API基础URL:', this.apiBase);

    this.init();
  }

  init() {
    this.createContextMenu();
    this.bindEvents();
  }

  createContextMenu() {
    // 创建右键菜单
    chrome.contextMenus.create({
      id: 'addToPromptHub',
      title: '添加到 Prompt Hub',
      contexts: ['selection'],
      documentUrlPatterns: ['http://*/*', 'https://*/*']
    });

    chrome.contextMenus.create({
      id: 'openPromptHub',
      title: '打开 Prompt Hub',
      contexts: ['page'],
      documentUrlPatterns: ['http://*/*', 'https://*/*']
    });
  }

  bindEvents() {
    // 右键菜单点击事件
    chrome.contextMenus.onClicked.addListener((info, tab) => {
      if (info.menuItemId === 'addToPromptHub') {
        this.handleAddPrompt(info, tab);
      } else if (info.menuItemId === 'openPromptHub') {
        this.openPromptHub();
      }
    });

    // 扩展安装事件
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.showWelcomeNotification();
      }
    });

    // 消息监听
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'addPrompt') {
        this.addPromptToServer(request.data)
          .then(result => sendResponse({ success: true, data: result }))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 保持消息通道开放
      }
    });
  }

  async handleAddPrompt(info, tab) {
    const selectedText = info.selectionText;
    if (!selectedText || selectedText.trim().length === 0) {
      this.showNotification('请先选择要添加的文本', 'error');
      return;
    }

    try {
      // 获取页面标题作为提示词名称
      const pageTitle = tab.title || '未命名提示词';
      const pageUrl = tab.url;
      
      // 构造提示词数据
      const promptData = {
        name: this.generatePromptName(selectedText, pageTitle),
        content: selectedText.trim(),
        source: this.extractDomain(pageUrl),
        notes: `来自: ${pageTitle}\n链接: ${pageUrl}`,
        tags: ['浏览器扩展', '网页收集']
      };

      // 发送到服务器
      const result = await this.addPromptToServer(promptData);
      
      this.showNotification('✅ 提示词已成功添加到 Prompt Hub', 'success');
      
    } catch (error) {
      console.error('添加提示词失败:', error);
      this.showNotification('❌ 添加失败: ' + error.message, 'error');
    }
  }

  async addPromptToServer(promptData) {
    try {
      // 使用配置文件中的API端点
      const apiUrl = this.config.getApiEndpoint ?
        this.config.getApiEndpoint('prompts', 'create') :
        `${this.apiBase}/prompts`;

      const headers = this.requestConfig.headers || {
        'Content-Type': 'application/json',
      };

      console.log('发送提示词到服务器:', apiUrl);
      console.log('当前环境配置:', this.config.currentEnvironment);
      console.log('请求数据:', promptData);

      // 添加超时控制
      const timeout = this.requestConfig.timeout || 10000;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(promptData),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`服务器错误 (${response.status}): ${errorText}`);
      }

      const result = await response.json();
      console.log('服务器响应成功:', result);
      return result;

    } catch (error) {
      console.error('添加提示词到服务器失败:', error);

      // 提供更详细的错误信息
      let errorMessage = '添加失败';

      if (error.name === 'AbortError') {
        errorMessage = '请求超时，请检查网络连接';
      } else if (error.message.includes('Failed to fetch')) {
        errorMessage = '无法连接到服务器，请确保服务器正在运行';
        console.error('网络连接失败，当前API地址:', this.apiBase);
        console.error('请检查服务器是否在运行:', this.config.getCurrentEnvironment?.()?.baseUrl || 'http://localhost:18080');
      } else if (error.message.includes('CORS')) {
        errorMessage = '跨域请求被阻止，请检查服务器CORS配置';
      } else {
        errorMessage = error.message || '未知错误';
      }

      throw new Error(errorMessage);
    }
  }

  generatePromptName(content, pageTitle) {
    // 从内容中提取前几个词作为名称
    const words = content.trim().split(/\s+/).slice(0, 8).join(' ');
    const maxLength = 50;
    
    if (words.length <= maxLength) {
      return words;
    }
    
    return words.substring(0, maxLength - 3) + '...';
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return '未知来源';
    }
  }

  openPromptHub() {
    // 使用配置文件中的基础URL
    const baseUrl = this.config.getCurrentEnvironment ?
      this.config.getCurrentEnvironment().baseUrl :
      'http://localhost:18080';

    console.log('打开 Prompt Hub 网页版:', baseUrl);
    chrome.tabs.create({ url: baseUrl });
  }

  showNotification(message, type = 'info') {
    const iconUrl = type === 'error' ? 'icons/icon32.png' : 'icons/icon32.png';
    
    chrome.notifications.create({
      type: 'basic',
      iconUrl: iconUrl,
      title: 'Prompt Hub',
      message: message
    });
  }

  showWelcomeNotification() {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'Prompt Hub 扩展已安装',
      message: '右键选择文本可快速添加提示词，点击扩展图标查看所有提示词。'
    });
  }
}

// 初始化后台脚本
new PromptHubBackground();
