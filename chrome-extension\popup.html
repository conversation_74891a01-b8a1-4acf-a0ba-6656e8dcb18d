<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prompt Hub</title>
  <link rel="stylesheet" href="popup.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <!-- 列表视图 -->
    <div id="listView" class="view">
      <!-- 头部 -->
      <div class="header">
        <div class="logo">
          <img src="icons/icon32.png" alt="Prompt Hub">
          <h1>Prompt Hub</h1>
        </div>
        <div class="actions">
          <button id="createBtn" class="btn-icon" title="创建提示词">
            <i class="material-icons">add</i>
          </button>
          <button id="refreshBtn" class="btn-icon" title="刷新">
            <i class="material-icons">refresh</i>
          </button>
          <button id="openWebBtn" class="btn-icon" title="打开网页版">
            <i class="material-icons">open_in_new</i>
          </button>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="search-container">
        <input type="text" id="searchInput" placeholder="搜索提示词..." class="search-input">
        <button id="searchBtn" class="search-btn">
          <i class="material-icons">search</i>
        </button>
      </div>

      <!-- 分类过滤器 -->
      <div class="category-filter">
        <div class="category-buttons">
          <button class="category-btn active" data-category="all">全部</button>
          <button class="category-btn" data-category="职业">职业</button>
          <button class="category-btn" data-category="商业">商业</button>
          <button class="category-btn" data-category="工具">工具</button>
          <button class="category-btn" data-category="语言">语言</button>
          <button class="category-btn" data-category="办公">办公</button>
          <button class="category-btn" data-category="写作">写作</button>
          <button class="category-btn" data-category="编程">编程</button>
        </div>
      </div>

      <!-- 状态显示 -->
      <div id="statusMessage" class="status-message hidden"></div>

      <!-- 提示词列表 -->
      <div class="prompts-container">
        <div id="loadingSpinner" class="loading">
          <div class="spinner"></div>
          <span>正在加载提示词...</span>
        </div>

        <div id="errorMessage" class="error-message hidden">
          <div class="error-icon">⚠️</div>
          <div class="error-text">
            <h3>连接失败</h3>
            <p>无法连接到 Prompt Hub 服务器</p>
            <p>请确保服务器正在运行：<br><code>http://localhost:18080</code></p>
            <button id="retryBtn" class="btn-primary">重试连接</button>
          </div>
        </div>

        <div id="promptsList" class="prompts-list hidden"></div>

        <div id="emptyState" class="empty-state hidden">
          <div class="empty-icon">📝</div>
          <h3>暂无提示词</h3>
          <p>点击右上角按钮打开网页版添加提示词</p>
        </div>
      </div>

      <!-- 底部 -->
      <div class="footer">
        <div class="tips">
          💡 右键选中文本可快速添加提示词
        </div>
      </div>
    </div>

    <!-- 编辑视图 -->
    <div id="editView" class="view hidden">
      <!-- 编辑头部 -->
      <div class="edit-header">
        <div class="edit-header-left">
          <button id="backToListBtn" class="btn-icon" title="返回列表">
            <i class="material-icons">arrow_back</i>
          </button>
          <h2 id="editTitle">编辑提示词</h2>
        </div>
        <div class="edit-header-right">
          <button id="savePromptBtn" class="btn-save">
            <i class="material-icons">save</i>
            <span>保存</span>
          </button>
        </div>
      </div>

      <!-- 编辑表单 -->
      <div class="edit-container">
        <form id="editForm" class="edit-form">
          <div class="form-group">
            <label for="editPromptName" class="form-label">
              <span class="required">*</span>
              标题
            </label>
            <input
              type="text"
              id="editPromptName"
              name="name"
              class="form-input"
              placeholder="输入提示词标题"
              required
            >
          </div>

          <div class="form-group">
            <label for="editPromptContent" class="form-label">
              <span class="required">*</span>
              内容
            </label>
            <div class="textarea-container">
              <textarea
                id="editPromptContent"
                name="content"
                class="form-textarea"
                placeholder="输入提示词内容"
                rows="6"
                required
              ></textarea>
              <div class="char-counter">
                <span id="editCharCount">0</span> 字符
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="editPromptTags" class="form-label">标签</label>
            <input
              type="text"
              id="editPromptTags"
              name="tags"
              class="form-input"
              placeholder="用逗号分隔多个标签，如：工作,效率,AI"
            >
          </div>

          <div class="form-group">
            <label for="editPromptSource" class="form-label">来源</label>
            <input
              type="text"
              id="editPromptSource"
              name="source"
              class="form-input"
              placeholder="来源链接或描述"
            >
          </div>

          <div class="form-group">
            <label for="editPromptNotes" class="form-label">备注</label>
            <textarea
              id="editPromptNotes"
              name="notes"
              class="form-textarea"
              placeholder="添加备注信息..."
              rows="2"
            ></textarea>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- 右键菜单 -->
  <div id="contextMenu" class="context-menu hidden">
    <div class="context-menu-item" id="copyAndEditBtn">
      <i class="material-icons">content_copy</i>
      <span>复制并编辑</span>
    </div>
  </div>

  <!-- 确认对话框 -->
  <div id="confirmDialog" class="confirm-dialog hidden">
    <div class="confirm-overlay"></div>
    <div class="confirm-content">
      <div class="confirm-header">
        <i class="material-icons">warning</i>
        <h3>确认删除</h3>
      </div>
      <p id="confirmMessage">确定要删除这个提示词吗？</p>
      <div class="confirm-actions">
        <button id="confirmCancel" class="btn-cancel">取消</button>
        <button id="confirmDelete" class="btn-delete">删除</button>
      </div>
    </div>
  </div>

  <script src="server-config.js"></script>
  <script src="popup.js"></script>
</body>
</html>
