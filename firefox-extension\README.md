# Prompt Tools Firefox 扩展

这是 Prompt Tools 项目的 Firefox 浏览器扩展，让您可以在任何网页上快速管理和使用 AI 提示词。

## 🚀 功能特性

### 核心功能
- **📋 快速查看**：点击扩展图标查看所有已保存的提示词
- **⚡ 一键复制**：点击任意提示词即可复制到剪贴板
- **🔍 智能搜索**：支持按名称、内容、标签搜索提示词
- **➕ 右键添加**：选中网页文本后右键选择"添加到 Prompt Tools"
- **⌨️ 快捷键**：使用 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac) 快速添加选中文本

### 🆕 配置管理特性
- **🔧 独立配置文件**：通过 `server-config.js` 统一管理所有配置
- **🌍 多环境支持**：开发、测试、生产环境快速切换
- **⚙️ 灵活配置**：服务器地址、API路径、超时时间等可自定义
- **🎛️ 扩展设置**：通知、快捷键、界面主题等个性化配置
- **📝 中文文档**：完整的中文配置指南和示例

### 界面特点
- 🎨 现代化设计，与主项目风格一致
- 🌏 完整中文界面
- 📱 响应式布局，适配不同屏幕
- ✨ 流畅的动画效果和用户反馈

## 📦 安装步骤

### 前置条件
确保 Prompt Tools 服务器正在运行：
```bash
# 在项目根目录执行
npm start
# 或
node server/src/app.js
```
服务器应该运行在 `http://localhost:18080`

### 安装扩展

1. **准备扩展文件**
   ```bash
   # 确保扩展文件夹存在
   cd firefox-extension/
   ls -la  # 应该看到 manifest.json 等文件
   ```

2. **在 Firefox 中加载扩展**
   - 打开 Firefox 浏览器
   - 访问 `about:debugging`
   - 点击左侧的"此 Firefox"
   - 点击"临时载入附加组件"
   - 选择 `firefox-extension` 文件夹中的 `manifest.json` 文件
   - 扩展安装完成！

## 🎯 使用教程

### 查看和复制提示词
1. 点击浏览器工具栏中的 Prompt Tools 图标
2. 在弹窗中浏览所有提示词
3. 使用搜索框快速查找特定提示词
4. 点击任意提示词即可复制到剪贴板

### 添加新提示词

**方法一：右键菜单**
1. 在任何网页上选中想要保存的文本
2. 右键点击选中的文本
3. 选择"添加到 Prompt Tools"
4. 系统会自动保存并显示成功提示

**方法二：快捷键**
1. 选中网页上的文本
2. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
3. 文本会自动添加到 Prompt Tools

### 管理提示词
- 点击扩展弹窗右上角的"打开网页版"按钮
- 在完整的 Web 界面中进行详细管理

## 🔧 技术实现

### 架构设计
```
Firefox Extension
├── manifest.json        # 扩展配置文件 (WebExtensions格式)
├── server-config.js     # 服务器配置文件
├── config-examples.js   # 配置示例文件
├── CONFIG_GUIDE.md      # 配置指南文档
├── popup.html/css/js    # 弹窗界面
├── background.js        # 后台脚本（持久化背景页面）
├── content.js           # 内容脚本（快捷键、页面交互）
└── icons/              # 扩展图标
```

### Firefox 特性
- **WebExtensions API**：使用标准的 browser.* API
- **持久化背景页面**：替代Chrome的Service Worker
- **Firefox兼容性**：针对Firefox特定行为进行优化
- **安全策略**：遵循Firefox的内容安全策略要求

### API 通信
扩展通过配置文件中的设置与 Prompt Tools 服务器通信：
- **获取提示词**：`GET {baseUrl}{apiPath}/prompts`
- **添加提示词**：`POST {baseUrl}{apiPath}/prompts`
- **健康检查**：`GET {baseUrl}{apiPath}/health`

### 权限说明
扩展请求的权限及用途：
- `activeTab`：获取当前标签页信息
- `contextMenus`：创建右键菜单
- `storage`：存储扩展设置
- `clipboardWrite`：复制文本到剪贴板
- `<all_urls>`：访问所有网站（用于内容脚本）
- `http://localhost:18080/*`：访问本地服务器 API

## ⚙️ 配置管理

### 快速配置
1. **查看配置文档**：阅读 [CONFIG_GUIDE.md](./CONFIG_GUIDE.md) 了解详细配置方法
2. **选择环境**：编辑 `server-config.js` 中的 `CURRENT_ENVIRONMENT` 变量
3. **自定义服务器**：修改对应环境的 `baseUrl` 配置
4. **重载扩展**：在 Firefox 调试页面重新加载扩展

## 🔄 Chrome扩展差异

### 主要差异
1. **Manifest格式**：使用WebExtensions格式而非Manifest V3
2. **背景脚本**：使用持久化背景页面而非Service Worker
3. **API调用**：使用 `browser.*` API而非 `chrome.*` API
4. **权限声明**：Firefox特定的权限格式
5. **安装方式**：通过about:debugging临时加载

### 兼容性
- 功能完全兼容Chrome版本
- 界面和用户体验保持一致
- 配置文件通用，支持多环境切换

## 🐛 故障排除

### 常见问题
1. **扩展无法加载**：检查manifest.json语法是否正确
2. **无法连接服务器**：确认Prompt Tools服务器正在运行
3. **右键菜单不显示**：检查contextMenus权限是否正确声明
4. **快捷键不工作**：确认快捷键没有与其他扩展冲突

### 调试方法
1. 打开 `about:debugging`
2. 找到 Prompt Tools 扩展
3. 点击"检查"按钮查看控制台输出
4. 检查背景页面和内容脚本的错误信息

## 📄 许可证

本项目遵循与主项目相同的许可证。

## 🤝 贡献

欢迎提交问题报告和功能请求！
