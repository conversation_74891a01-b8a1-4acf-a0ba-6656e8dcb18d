<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>服务器连接测试工具</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    .test-section {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    .test-button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
      font-size: 14px;
    }
    .test-button:hover {
      background: #0056b3;
    }
    .test-button:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }
    .test-result {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      margin: 10px 0;
      min-height: 50px;
      font-family: monospace;
      font-size: 12px;
      white-space: pre-wrap;
    }
    .success {
      border-color: #28a745;
      background: #d4edda;
      color: #155724;
    }
    .error {
      border-color: #dc3545;
      background: #f8d7da;
      color: #721c24;
    }
    .warning {
      border-color: #ffc107;
      background: #fff3cd;
      color: #856404;
    }
    .info {
      border-color: #17a2b8;
      background: #d1ecf1;
      color: #0c5460;
    }
    .server-info {
      background: #e3f2fd;
      border: 1px solid #2196f3;
      padding: 15px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <h1>🔧 Prompt Hub 服务器连接测试工具</h1>
  
  <div class="test-section">
    <h2>📋 测试说明</h2>
    <p>这个工具用于诊断 Chrome 扩展中的 "Failed to fetch" 错误。请按照以下步骤进行测试：</p>
    <ol>
      <li>确保已修改配置文件中的环境设置</li>
      <li>点击下面的测试按钮</li>
      <li>观察测试结果和错误信息</li>
      <li>根据结果采取相应的修复措施</li>
    </ol>
  </div>

  <div class="test-section">
    <h2>🌐 当前配置信息</h2>
    <div id="configInfo" class="server-info">
      <div class="loading"></div> 正在加载配置信息...
    </div>
  </div>

  <div class="test-section">
    <h2>🧪 连接测试</h2>
    
    <h3>测试按钮：</h3>
    <button class="test-button" onclick="testLocalServer()">测试本地服务器 (localhost:18080)</button>
    <button class="test-button" onclick="testTestingServer()">测试测试服务器 (************:18080)</button>
    <button class="test-button" onclick="testCurrentConfig()">测试当前配置的服务器</button>
    <button class="test-button" onclick="testApiEndpoint()">测试 API 端点</button>
    <button class="test-button" onclick="clearResults()">清除结果</button>
    
    <h3>测试结果：</h3>
    <div id="testResults" class="test-result">
      点击上面的按钮开始测试...
    </div>
  </div>

  <div class="test-section">
    <h2>🔧 修复建议</h2>
    <div id="fixSuggestions" class="test-result info">
      等待测试结果...
    </div>
  </div>

  <script src="server-config.js"></script>
  <script>
    // 显示配置信息
    function showConfigInfo() {
      const configDiv = document.getElementById('configInfo');
      const config = window.PromptToolsConfig || {};
      
      const currentEnv = config.getCurrentEnvironment ? config.getCurrentEnvironment() : null;
      const apiBase = config.getApiBaseUrl ? config.getApiBaseUrl() : 'http://localhost:18080/api';
      
      configDiv.innerHTML = `
        <strong>当前环境：</strong>${config.currentEnvironment || '未知'}<br>
        <strong>服务器地址：</strong>${currentEnv ? currentEnv.baseUrl : '未知'}<br>
        <strong>API 路径：</strong>${currentEnv ? currentEnv.apiPath : '未知'}<br>
        <strong>完整 API URL：</strong>${apiBase}<br>
        <strong>超时时间：</strong>${currentEnv ? currentEnv.timeout + 'ms' : '未知'}<br>
        <strong>重试次数：</strong>${currentEnv ? currentEnv.retryAttempts : '未知'}
      `;
    }
    
    // 显示测试结果
    function showResult(message, type = 'info') {
      const results = document.getElementById('testResults');
      const timestamp = new Date().toLocaleTimeString();
      results.className = `test-result ${type}`;
      results.innerHTML = `[${timestamp}] ${message}`;
    }
    
    // 显示修复建议
    function showFixSuggestions(suggestions) {
      const fixDiv = document.getElementById('fixSuggestions');
      fixDiv.innerHTML = suggestions;
    }
    
    // 测试服务器连接
    async function testServer(url, description) {
      showResult(`正在测试 ${description}...\n服务器地址: ${url}`, 'info');
      
      try {
        const startTime = Date.now();
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        if (response.ok) {
          const data = await response.text();
          showResult(`✅ ${description} 连接成功！
响应时间: ${responseTime}ms
状态码: ${response.status}
响应内容: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`, 'success');
          
          showFixSuggestions(`
            ✅ 服务器连接正常！
            
            如果扩展仍然报错，请检查：
            1. 重新加载 Chrome 扩展
            2. 检查扩展的权限配置
            3. 查看扩展控制台的详细错误信息
          `);
        } else {
          showResult(`❌ ${description} 响应错误
状态码: ${response.status}
状态文本: ${response.statusText}
响应时间: ${responseTime}ms`, 'error');
          
          showFixSuggestions(`
            ❌ 服务器响应错误 (${response.status})
            
            修复建议：
            1. 检查服务器是否正确启动
            2. 验证 API 端点是否存在
            3. 检查服务器日志中的错误信息
          `);
        }
        
      } catch (error) {
        showResult(`❌ ${description} 连接失败
错误类型: ${error.name}
错误信息: ${error.message}`, 'error');
        
        let suggestions = '';
        if (error.message.includes('Failed to fetch')) {
          suggestions = `
            ❌ 网络连接失败
            
            修复建议：
            1. 确保服务器正在运行
            2. 检查服务器地址是否正确
            3. 检查防火墙设置
            4. 如果是测试服务器，确保网络连接正常
            
            推荐操作：
            - 修改 server-config.js 中的 CURRENT_ENVIRONMENT 为 'development'
            - 启动本地服务器: npm start
          `;
        } else if (error.message.includes('CORS')) {
          suggestions = `
            ❌ 跨域请求被阻止
            
            修复建议：
            1. 检查服务器的 CORS 配置
            2. 确保服务器允许来自扩展的请求
          `;
        } else {
          suggestions = `
            ❌ 未知网络错误
            
            修复建议：
            1. 检查网络连接
            2. 重启浏览器
            3. 检查扩展权限
          `;
        }
        
        showFixSuggestions(suggestions);
      }
    }
    
    // 测试本地服务器
    function testLocalServer() {
      testServer('http://localhost:18080/api/health', '本地开发服务器');
    }
    
    // 测试测试服务器
    function testTestingServer() {
      testServer('http://************:18080/api/health', '测试服务器');
    }
    
    // 测试当前配置的服务器
    function testCurrentConfig() {
      const config = window.PromptToolsConfig || {};
      const apiBase = config.getApiBaseUrl ? config.getApiBaseUrl() : 'http://localhost:18080/api';
      const healthUrl = apiBase.replace('/api', '') + '/api/health';
      
      testServer(healthUrl, '当前配置的服务器');
    }
    
    // 测试 API 端点
    async function testApiEndpoint() {
      const config = window.PromptToolsConfig || {};
      const apiUrl = config.getApiEndpoint ? 
        config.getApiEndpoint('prompts', 'list') : 
        (config.getApiBaseUrl ? config.getApiBaseUrl() : 'http://localhost:18080/api') + '/prompts';
      
      showResult(`正在测试 API 端点...\nAPI 地址: ${apiUrl}`, 'info');
      
      try {
        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          showResult(`✅ API 端点测试成功！
状态码: ${response.status}
数据类型: ${Array.isArray(data) ? '数组' : typeof data}
数据长度: ${Array.isArray(data) ? data.length : '不适用'}`, 'success');
          
          showFixSuggestions(`
            ✅ API 端点正常工作！
            
            扩展应该能够正常添加提示词。
            如果仍有问题，请检查扩展的权限配置。
          `);
        } else {
          showResult(`❌ API 端点响应错误
状态码: ${response.status}
状态文本: ${response.statusText}`, 'error');
        }
        
      } catch (error) {
        showResult(`❌ API 端点测试失败
错误: ${error.message}`, 'error');
      }
    }
    
    // 清除结果
    function clearResults() {
      const results = document.getElementById('testResults');
      results.className = 'test-result';
      results.innerHTML = '结果已清除，点击按钮开始新的测试...';
      
      const fixDiv = document.getElementById('fixSuggestions');
      fixDiv.className = 'test-result info';
      fixDiv.innerHTML = '等待测试结果...';
    }
    
    // 页面加载时显示配置信息
    showConfigInfo();
    
    console.log('服务器连接测试工具已加载');
    console.log('当前配置:', window.PromptToolsConfig);
  </script>
</body>
</html>
