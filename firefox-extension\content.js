// Prompt Tools Firefox Extension - Content Script

class PromptToolsContent {
  constructor() {
    // 获取扩展配置
    this.config = window.PromptToolsConfig || {};
    this.extensionConfig = this.config.extensionConfig || {};

    console.log('Firefox内容脚本初始化 - 当前环境:', this.config.currentEnvironment);

    this.init();
  }

  init() {
    // 监听来自 popup 或 background 的消息 - 使用browser API
    browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'getSelectedText') {
        const selectedText = window.getSelection().toString().trim();
        sendResponse({ selectedText });
      }
      return true;
    });

    // 添加键盘快捷键支持
    this.addKeyboardShortcuts();
  }

  addKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
      // 使用配置文件中的快捷键设置，默认为 Ctrl+Shift+P
      const shortcutConfig = this.extensionConfig.shortcuts || {};
      const quickAddKey = shortcutConfig.quickAdd || 'Ctrl+Shift+P';

      // 解析快捷键配置（简单实现）
      const isCtrlShiftP = (event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'P';

      if (isCtrlShiftP) {
        event.preventDefault();
        console.log('触发快捷键:', quickAddKey);
        this.handleQuickAdd();
      }
    });
  }

  async handleQuickAdd() {
    const selectedText = window.getSelection().toString().trim();
    
    if (!selectedText) {
      this.showToast('请先选择要添加的文本', 'warning');
      return;
    }

    try {
      // 发送消息给 background script - 使用browser API
      const response = await browser.runtime.sendMessage({
        action: 'addPrompt',
        data: {
          name: this.generatePromptName(selectedText),
          content: selectedText,
          source: window.location.hostname,
          notes: `来自: ${document.title}\n链接: ${window.location.href}`,
          tags: ['快捷键添加', '网页收集']
        }
      });

      if (response.success) {
        this.showToast('✅ 提示词已添加到 Prompt Tools', 'success');
      } else {
        this.showToast('❌ 添加失败: ' + response.error, 'error');
      }
    } catch (error) {
      console.error('快捷键添加失败:', error);
      this.showToast('❌ 添加失败，请检查服务器连接', 'error');
    }
  }

  generatePromptName(content) {
    const words = content.split(/\s+/).slice(0, 8).join(' ');
    const maxLength = 50;
    
    if (words.length <= maxLength) {
      return words;
    }
    
    return words.substring(0, maxLength - 3) + '...';
  }

  showToast(message, type = 'info') {
    // 检查是否启用通知
    const notificationConfig = this.extensionConfig.notifications || {};
    if (notificationConfig.enabled === false) {
      console.log('通知已禁用:', message);
      return;
    }

    // 获取通知持续时间配置
    const duration = notificationConfig.duration || 3000;

    // 创建 toast 元素
    const toast = document.createElement('div');
    toast.className = `prompt-tools-toast prompt-tools-toast-${type}`;
    toast.textContent = message;

    // 添加样式
    this.addToastStyles();

    // 添加到页面
    document.body.appendChild(toast);

    console.log('显示通知:', message, '类型:', type, '持续时间:', duration + 'ms');

    // 显示动画
    setTimeout(() => {
      toast.classList.add('prompt-tools-toast-show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
      toast.classList.remove('prompt-tools-toast-show');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, duration);
  }

  addToastStyles() {
    // 检查是否已经添加过样式
    if (document.getElementById('prompt-tools-toast-styles')) {
      return;
    }

    const styles = document.createElement('style');
    styles.id = 'prompt-tools-toast-styles';
    styles.textContent = `
      .prompt-tools-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        padding: 12px 20px;
        border-radius: 8px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
        max-width: 300px;
        word-wrap: break-word;
      }
      
      .prompt-tools-toast-show {
        transform: translateX(0);
      }
      
      .prompt-tools-toast-success {
        background: #28a745;
      }
      
      .prompt-tools-toast-error {
        background: #dc3545;
      }
      
      .prompt-tools-toast-warning {
        background: #ffc107;
        color: #212529;
      }
      
      .prompt-tools-toast-info {
        background: #007bff;
      }
    `;
    
    document.head.appendChild(styles);
  }
}

// 初始化内容脚本
new PromptToolsContent();
