# Prompt Tools Chrome Extension 配置指南

## 概述

本文档介绍如何使用 `server-config.js` 配置文件来管理 Prompt Tools Chrome 扩展的服务器连接信息和各种设置。

## 配置文件结构

### 1. 服务器环境配置 (SERVER_ENVIRONMENTS)

配置文件支持多个环境配置，便于在不同开发阶段切换：

```javascript
const SERVER_ENVIRONMENTS = {
  development: {
    name: '开发环境',
    baseUrl: 'http://localhost:18080',
    apiPath: '/api',
    timeout: 10000,
    retryAttempts: 3,
    description: '本地开发服务器'
  },
  // ... 其他环境
};
```

#### 可用环境：
- **development**: 本地开发环境
- **testing**: 测试环境
- **production**: 生产环境
- **custom**: 自定义环境

### 2. 当前环境设置 (CURRENT_ENVIRONMENT)

通过修改此变量快速切换环境：

```javascript
const CURRENT_ENVIRONMENT = 'development'; // 可选: development, testing, production, custom
```

### 3. API 端点配置 (API_ENDPOINTS)

定义各种 API 接口路径：

```javascript
const API_ENDPOINTS = {
  prompts: {
    list: '/prompts',           // 获取提示词列表
    create: '/prompts',         // 创建新提示词
    update: '/prompts/{id}',    // 更新提示词
    delete: '/prompts/{id}',    // 删除提示词
    search: '/prompts/search'   // 搜索提示词
  },
  // ... 其他端点
};
```

### 4. 扩展功能配置 (EXTENSION_CONFIG)

控制扩展的各种功能设置：

```javascript
const EXTENSION_CONFIG = {
  notifications: {
    enabled: true,              // 是否启用通知
    duration: 3000,             // 通知显示时长（毫秒）
    position: 'top-right'       // 通知位置
  },
  shortcuts: {
    quickAdd: 'Ctrl+Shift+P',   // 快速添加快捷键
    openExtension: 'Ctrl+Shift+O' // 打开扩展快捷键
  },
  ui: {
    theme: 'light',             // 主题
    language: 'zh-CN',          // 语言设置
    maxPromptPreview: 100       // 提示词预览最大字符数
  },
  data: {
    cacheEnabled: true,         // 是否启用缓存
    cacheExpiry: 300000,        // 缓存过期时间（毫秒）
    autoSync: true              // 是否自动同步
  }
};
```

## 使用方法

### 1. 切换服务器环境

要切换到不同的服务器环境，只需修改 `CURRENT_ENVIRONMENT` 变量：

```javascript
// 切换到生产环境
const CURRENT_ENVIRONMENT = 'production';

// 切换到自定义环境
const CURRENT_ENVIRONMENT = 'custom';
```

### 2. 添加自定义服务器

在 `custom` 环境中配置您的自定义服务器：

```javascript
custom: {
  name: '我的服务器',
  baseUrl: 'http://*************:8080', // 修改为您的服务器地址
  apiPath: '/api',
  timeout: 12000,
  retryAttempts: 2,
  description: '自定义服务器地址'
}
```

### 3. 修改通知设置

```javascript
notifications: {
  enabled: false,             // 禁用通知
  duration: 5000,             // 延长通知显示时间到5秒
  position: 'top-left'        // 改变通知位置
}
```

### 4. 自定义快捷键

```javascript
shortcuts: {
  quickAdd: 'Ctrl+Alt+P',     // 修改快速添加快捷键
  openExtension: 'Ctrl+Alt+O' // 修改打开扩展快捷键
}
```

## 配置示例

### 示例1：开发环境配置

```javascript
const CURRENT_ENVIRONMENT = 'development';

// 开发环境通常使用本地服务器
SERVER_ENVIRONMENTS.development = {
  name: '本地开发',
  baseUrl: 'http://localhost:18080',
  apiPath: '/api',
  timeout: 10000,
  retryAttempts: 3,
  description: '本地开发服务器'
};
```

### 示例2：生产环境配置

```javascript
const CURRENT_ENVIRONMENT = 'production';

// 生产环境使用HTTPS和更长的超时时间
SERVER_ENVIRONMENTS.production = {
  name: '生产环境',
  baseUrl: 'https://api.prompttools.com',
  apiPath: '/api/v1',
  timeout: 20000,
  retryAttempts: 3,
  description: '生产环境服务器'
};
```

### 示例3：团队协作配置

```javascript
const CURRENT_ENVIRONMENT = 'custom';

// 团队共享的测试服务器
SERVER_ENVIRONMENTS.custom = {
  name: '团队测试服务器',
  baseUrl: 'http://test-server.company.com:8080',
  apiPath: '/api',
  timeout: 15000,
  retryAttempts: 2,
  description: '团队共享测试环境'
};
```

## 工具函数

配置文件提供了以下工具函数：

### getCurrentEnvironment()
获取当前环境配置对象

```javascript
const env = window.PromptToolsConfig.getCurrentEnvironment();
console.log('当前环境:', env.name);
```

### getApiBaseUrl()
获取完整的API基础URL

```javascript
const apiUrl = window.PromptToolsConfig.getApiBaseUrl();
console.log('API基础URL:', apiUrl);
```

### getApiEndpoint(category, endpoint, params)
获取特定API端点的完整URL

```javascript
// 获取提示词列表API
const listUrl = window.PromptToolsConfig.getApiEndpoint('prompts', 'list');

// 获取更新提示词API（带参数）
const updateUrl = window.PromptToolsConfig.getApiEndpoint('prompts', 'update', {id: 123});
```

### validateServerConnection()
验证服务器连接

```javascript
const isConnected = await window.PromptToolsConfig.validateServerConnection();
if (isConnected) {
  console.log('服务器连接正常');
} else {
  console.log('服务器连接失败');
}
```

## 故障排除

### 1. 扩展无法连接服务器

1. 检查 `CURRENT_ENVIRONMENT` 设置是否正确
2. 验证对应环境的 `baseUrl` 是否可访问
3. 确认服务器正在运行并监听正确的端口

### 2. API 调用失败

1. 检查 `apiPath` 配置是否正确
2. 验证 API 端点路径是否匹配服务器实际路径
3. 检查超时设置是否合理

### 3. 通知不显示

1. 检查 `notifications.enabled` 是否为 `true`
2. 验证页面是否允许显示通知
3. 检查浏览器控制台是否有错误信息

## 最佳实践

1. **环境分离**: 为不同的开发阶段使用不同的环境配置
2. **安全考虑**: 生产环境应使用 HTTPS 协议
3. **超时设置**: 根据网络条件调整合适的超时时间
4. **错误处理**: 配置合理的重试次数
5. **文档更新**: 修改配置后及时更新团队文档

## 注意事项

1. 修改配置文件后需要重新加载扩展才能生效
2. 确保服务器地址和端口配置正确
3. 生产环境建议使用 HTTPS 协议
4. 定期验证服务器连接状态
5. 备份重要的配置设置

---

如有问题，请参考扩展的其他文档或联系开发团队。
