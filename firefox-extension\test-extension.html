<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Firefox 扩展功能测试页面</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    .test-section {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    .test-item {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      margin: 10px 0;
    }
    .test-text {
      background: #e3f2fd;
      border: 2px dashed #2196f3;
      padding: 15px;
      margin: 10px 0;
      border-radius: 4px;
      cursor: pointer;
      user-select: text;
    }
    .test-text:hover {
      background: #bbdefb;
    }
    .status {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }
    .status.pending {
      background: #fff3cd;
      color: #856404;
    }
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    .instructions {
      background: #d1ecf1;
      border: 1px solid #bee5eb;
      border-radius: 4px;
      padding: 15px;
      margin: 10px 0;
    }
    .code {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 10px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <h1>🦊 Firefox 扩展功能测试页面</h1>
  
  <div class="instructions">
    <h3>📋 测试说明</h3>
    <p>本页面用于测试 Prompt Hub Firefox 扩展的各项功能。请按照以下步骤进行测试：</p>
    <ol>
      <li>确保 Prompt Tools 服务器正在运行（http://localhost:18080）</li>
      <li>确保 Firefox 扩展已正确安装并启用</li>
      <li>按照每个测试项目的说明进行操作</li>
      <li>观察测试结果并记录任何问题</li>
    </ol>
  </div>

  <div class="test-section">
    <h2>🔧 1. 扩展基础功能测试</h2>
    
    <div class="test-item">
      <h3>1.1 扩展图标显示 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>检查 Firefox 工具栏是否显示 Prompt Hub 图标</li>
        <li>图标应该是蓝色圆形设计</li>
        <li>鼠标悬停应显示"Prompt Hub"提示</li>
      </ul>
      <p><strong>预期结果：</strong>图标正常显示，样式正确</p>
    </div>

    <div class="test-item">
      <h3>1.2 弹窗界面测试 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>点击工具栏中的 Prompt Hub 图标</li>
        <li>观察弹窗是否正常打开</li>
        <li>检查界面元素是否完整显示</li>
      </ul>
      <p><strong>预期结果：</strong>弹窗正常打开，界面完整，中文显示正确</p>
    </div>
  </div>

  <div class="test-section">
    <h2>📝 2. 文本选择和添加功能测试</h2>
    
    <div class="test-item">
      <h3>2.1 右键菜单测试 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ol>
        <li>选中下面的测试文本</li>
        <li>右键点击选中的文本</li>
        <li>查看是否出现"添加到 Prompt Hub"选项</li>
        <li>点击该选项</li>
      </ol>
      
      <div class="test-text">
        这是一个用于测试右键菜单功能的示例文本。请选中这段文字，然后右键点击查看是否出现"添加到 Prompt Hub"选项。这个功能应该能够将选中的文本快速添加到提示词库中。
      </div>
      
      <p><strong>预期结果：</strong>右键菜单显示正确选项，点击后成功添加提示词</p>
    </div>

    <div class="test-item">
      <h3>2.2 快捷键测试 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ol>
        <li>选中下面的测试文本</li>
        <li>按 <code>Ctrl+Shift+P</code> (Windows/Linux) 或 <code>Cmd+Shift+P</code> (Mac)</li>
        <li>观察是否出现成功添加的提示</li>
      </ol>
      
      <div class="test-text">
        这是一个用于测试快捷键功能的示例文本。请选中这段文字，然后按 Ctrl+Shift+P 快捷键。这个功能应该能够快速将选中的文本添加到提示词库中，无需使用右键菜单。
      </div>
      
      <p><strong>预期结果：</strong>快捷键响应正常，显示成功添加提示</p>
    </div>
  </div>

  <div class="test-section">
    <h2>🔍 3. 提示词管理功能测试</h2>
    
    <div class="test-item">
      <h3>3.1 提示词列表显示 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>点击扩展图标打开弹窗</li>
        <li>检查是否显示提示词列表</li>
        <li>验证提示词内容、标签、来源等信息</li>
      </ul>
      <p><strong>预期结果：</strong>提示词列表正常显示，信息完整</p>
    </div>

    <div class="test-item">
      <h3>3.2 搜索功能测试 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>在弹窗的搜索框中输入关键词</li>
        <li>观察搜索结果是否正确过滤</li>
        <li>测试按标签搜索功能</li>
      </ul>
      <p><strong>预期结果：</strong>搜索功能正常，结果准确</p>
    </div>

    <div class="test-item">
      <h3>3.3 复制功能测试 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>点击任意提示词项目</li>
        <li>检查是否显示"已复制到剪贴板"提示</li>
        <li>在文本编辑器中粘贴验证内容</li>
      </ul>
      <p><strong>预期结果：</strong>复制功能正常，内容正确</p>
    </div>
  </div>

  <div class="test-section">
    <h2>🌐 4. 网络连接和配置测试</h2>
    
    <div class="test-item">
      <h3>4.1 服务器连接测试 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>确保服务器运行在 http://localhost:18080</li>
        <li>点击扩展图标</li>
        <li>观察是否能正常加载数据</li>
      </ul>
      <p><strong>预期结果：</strong>成功连接服务器，数据加载正常</p>
    </div>

    <div class="test-item">
      <h3>4.2 错误处理测试 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>停止 Prompt Tools 服务器</li>
        <li>点击扩展图标</li>
        <li>观察错误提示是否友好</li>
        <li>点击"重试连接"按钮</li>
      </ul>
      <p><strong>预期结果：</strong>显示友好的错误信息，重试功能正常</p>
    </div>

    <div class="test-item">
      <h3>4.3 网页版跳转测试 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>在弹窗中点击"打开网页版"按钮</li>
        <li>观察是否在新标签页中打开网页版</li>
        <li>验证网页版功能是否正常</li>
      </ul>
      <p><strong>预期结果：</strong>成功跳转到网页版，功能正常</p>
    </div>
  </div>

  <div class="test-section">
    <h2>🔧 5. 开发者功能测试</h2>
    
    <div class="test-item">
      <h3>5.1 控制台日志检查 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>打开 Firefox 开发者工具 (F12)</li>
        <li>切换到控制台标签</li>
        <li>执行各项扩展功能</li>
        <li>观察是否有错误信息</li>
      </ul>
      <p><strong>预期结果：</strong>无严重错误，日志信息正常</p>
    </div>

    <div class="test-item">
      <h3>5.2 扩展调试信息 <span class="status pending">待测试</span></h3>
      <p><strong>测试步骤：</strong></p>
      <ul>
        <li>访问 about:debugging</li>
        <li>找到 Prompt Hub 扩展</li>
        <li>点击"检查"按钮</li>
        <li>查看背景页面的调试信息</li>
      </ul>
      <p><strong>预期结果：</strong>调试信息正常，无错误</p>
    </div>
  </div>

  <div class="test-section">
    <h2>📊 6. 测试结果记录</h2>
    
    <div class="test-item">
      <h3>测试环境信息</h3>
      <div class="code">
        <strong>Firefox 版本：</strong><span id="firefoxVersion">请在控制台运行 navigator.userAgent 查看</span><br>
        <strong>操作系统：</strong><span id="osInfo">请在控制台运行 navigator.platform 查看</span><br>
        <strong>测试时间：</strong><span id="testTime"></span><br>
        <strong>服务器状态：</strong><span id="serverStatus">待检查</span>
      </div>
    </div>

    <div class="test-item">
      <h3>问题记录</h3>
      <textarea id="issueLog" style="width: 100%; height: 100px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" placeholder="请在此记录测试过程中发现的问题..."></textarea>
    </div>
  </div>

  <script>
    // 设置测试时间
    document.getElementById('testTime').textContent = new Date().toLocaleString('zh-CN');
    
    // 检查服务器状态
    async function checkServerStatus() {
      try {
        const response = await fetch('http://localhost:18080/api/health');
        if (response.ok) {
          document.getElementById('serverStatus').textContent = '✅ 运行正常';
          document.getElementById('serverStatus').style.color = 'green';
        } else {
          document.getElementById('serverStatus').textContent = '❌ 响应异常';
          document.getElementById('serverStatus').style.color = 'red';
        }
      } catch (error) {
        document.getElementById('serverStatus').textContent = '❌ 连接失败';
        document.getElementById('serverStatus').style.color = 'red';
      }
    }
    
    // 页面加载时检查服务器状态
    checkServerStatus();
    
    // 添加测试文本的点击提示
    document.querySelectorAll('.test-text').forEach(element => {
      element.addEventListener('click', function() {
        // 选中文本
        const range = document.createRange();
        range.selectNodeContents(this);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
        
        // 显示提示
        const originalText = this.textContent;
        this.style.background = '#c8e6c9';
        setTimeout(() => {
          this.style.background = '#e3f2fd';
        }, 1000);
      });
    });
    
    console.log('🦊 Firefox 扩展测试页面已加载');
    console.log('浏览器信息:', navigator.userAgent);
    console.log('平台信息:', navigator.platform);
  </script>
</body>
</html>
