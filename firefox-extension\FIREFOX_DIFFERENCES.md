# Firefox 扩展与 Chrome 扩展的差异说明

## 📋 概述

本文档详细说明了 Firefox 版本的 Prompt Hub 扩展与 Chrome 版本之间的主要差异，以及转换过程中的技术细节。

## 🔄 主要差异

### 1. Manifest 文件格式

#### Chrome (Manifest V3)
```json
{
  "manifest_version": 3,
  "background": {
    "service_worker": "background.js"
  },
  "action": {
    "default_popup": "popup.html"
  },
  "host_permissions": [
    "http://localhost:18080/*"
  ]
}
```

#### Firefox (WebExtensions)
```json
{
  "manifest_version": 2,
  "background": {
    "scripts": ["server-config.js", "background.js"],
    "persistent": true
  },
  "browser_action": {
    "default_popup": "popup.html"
  },
  "permissions": [
    "http://localhost:18080/*"
  ],
  "applications": {
    "gecko": {
      "id": "<EMAIL>",
      "strict_min_version": "57.0"
    }
  }
}
```

### 2. API 调用差异

#### Chrome API
```javascript
// Chrome 使用 chrome.* API
chrome.tabs.create({ url: baseUrl });
chrome.contextMenus.create({...});
chrome.runtime.onMessage.addListener(...);
```

#### Firefox API
```javascript
// Firefox 使用 browser.* API
browser.tabs.create({ url: baseUrl });
browser.contextMenus.create({...});
browser.runtime.onMessage.addListener(...);
```

### 3. 背景脚本差异

#### Chrome (Service Worker)
```javascript
// Chrome 使用 Service Worker
class PromptHubBackground {
  constructor() {
    // Service Worker 中使用 self
    this.config = self.PromptToolsConfig || {};
    // 使用 importScripts 导入脚本
    importScripts('server-config.js');
  }
}
```

#### Firefox (持久化背景页面)
```javascript
// Firefox 使用持久化背景页面
class PromptHubBackground {
  constructor() {
    // 背景页面中使用 window
    this.config = window.PromptToolsConfig || {};
    // 在 manifest.json 中声明脚本
  }
}
```

### 4. 权限声明差异

#### Chrome
- 使用 `host_permissions` 数组
- 权限更细粒度控制
- 支持 `activeTab` 权限

#### Firefox
- 权限直接在 `permissions` 数组中
- 需要明确声明所有需要的权限
- 支持通配符权限

## 🛠️ 技术实现细节

### 1. 配置文件兼容性

`server-config.js` 文件在两个版本中保持完全兼容：

```javascript
// 自动检测环境
if (typeof window !== 'undefined') {
  // 浏览器环境（Firefox/Chrome）
  window.PromptToolsConfig = {...};
} else {
  // Node.js 环境
  module.exports = {...};
}
```

### 2. 热重载机制

#### Chrome 版本
- 使用 Service Worker 兼容的热重载
- 通过 `chrome.runtime.reload()` 重载

#### Firefox 版本
- 简化的热重载机制
- 通过 `browser.runtime.reload()` 或页面刷新

### 3. 内容安全策略

#### Chrome
```json
// 默认的严格 CSP
"content_security_policy": {
  "extension_pages": "script-src 'self'; object-src 'self'"
}
```

#### Firefox
```json
// 需要明确声明 CSP
"content_security_policy": "script-src 'self' 'unsafe-eval'; object-src 'self'"
```

## 🔧 开发差异

### 1. 调试方式

#### Chrome
- 访问 `chrome://extensions/`
- 开启开发者模式
- 点击"检查视图"查看背景页面

#### Firefox
- 访问 `about:debugging`
- 点击"此 Firefox"
- 点击"检查"查看背景页面

### 2. 安装方式

#### Chrome
- 加载已解压的扩展程序
- 支持开发者模式

#### Firefox
- 临时载入附加组件
- 支持 XPI 文件安装

### 3. 错误处理

#### Chrome
```javascript
// Chrome 错误处理
chrome.runtime.lastError && console.error(chrome.runtime.lastError);
```

#### Firefox
```javascript
// Firefox 错误处理
// 通常通过 Promise 的 catch 处理
browser.tabs.create(url).catch(console.error);
```

## 📊 功能对比

| 功能 | Chrome 版本 | Firefox 版本 | 兼容性 |
|------|-------------|--------------|--------|
| 弹窗界面 | ✅ | ✅ | 完全兼容 |
| 右键菜单 | ✅ | ✅ | 完全兼容 |
| 快捷键 | ✅ | ✅ | 完全兼容 |
| 通知 | ✅ | ✅ | 完全兼容 |
| 剪贴板 | ✅ | ✅ | 完全兼容 |
| 热重载 | ✅ | ✅ (简化) | 基本兼容 |
| 配置管理 | ✅ | ✅ | 完全兼容 |

## 🚀 性能对比

### 内存使用
- **Chrome**: Service Worker 模式，内存使用更高效
- **Firefox**: 持久化背景页面，内存使用稍高

### 启动速度
- **Chrome**: Service Worker 按需启动，启动更快
- **Firefox**: 背景页面持续运行，响应更快

### API 响应
- **Chrome**: 异步 API，性能更好
- **Firefox**: 同样支持异步，性能相当

## 🔄 迁移指南

### 从 Chrome 迁移到 Firefox

1. **修改 manifest.json**
   - 改为 manifest_version 2
   - 使用 browser_action 替代 action
   - 调整权限声明格式

2. **更新 API 调用**
   - 将所有 `chrome.*` 替换为 `browser.*`
   - 检查 API 兼容性

3. **调整背景脚本**
   - 移除 Service Worker 特定代码
   - 使用 window 对象替代 self

4. **测试功能**
   - 验证所有功能正常工作
   - 检查错误处理

### 从 Firefox 迁移到 Chrome

1. **升级 manifest.json**
   - 改为 manifest_version 3
   - 使用 service_worker 替代 scripts
   - 调整权限格式

2. **更新背景脚本**
   - 适配 Service Worker 环境
   - 使用 importScripts 导入依赖

3. **API 兼容性**
   - 检查 Chrome 特定 API
   - 处理异步差异

## 📝 最佳实践

### 1. 跨浏览器兼容
```javascript
// 使用统一的 API 接口
const browserAPI = typeof browser !== 'undefined' ? browser : chrome;
browserAPI.tabs.create({ url: baseUrl });
```

### 2. 错误处理
```javascript
// 统一的错误处理
function handleError(error) {
  console.error('扩展错误:', error);
  // 显示用户友好的错误信息
}
```

### 3. 配置管理
```javascript
// 使用统一的配置系统
const config = window.PromptToolsConfig || {};
const apiBase = config.getApiBaseUrl();
```

## 🎯 总结

Firefox 版本的 Prompt Hub 扩展在功能上与 Chrome 版本完全兼容，主要差异在于：

1. **技术架构**: 使用 WebExtensions API 和持久化背景页面
2. **安装方式**: 通过 about:debugging 临时加载或 XPI 文件安装
3. **调试方法**: 使用 Firefox 开发者工具进行调试
4. **性能特性**: 背景页面持续运行，响应更快但内存使用稍高

两个版本都提供相同的用户体验和功能特性，用户可以根据浏览器偏好选择使用。
