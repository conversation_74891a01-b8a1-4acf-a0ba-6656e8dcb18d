// Prompt Tools Chrome Extension - 服务器配置文件
// 此文件用于管理后端服务器连接信息和环境配置

/**
 * 服务器环境配置
 * 可以根据需要切换不同的环境
 */
const SERVER_ENVIRONMENTS = {
  // 开发环境配置
  development: {
    name: '开发环境',
    baseUrl: 'http://localhost:18080',
    apiPath: '/api',
    timeout: 10000, // 请求超时时间（毫秒）
    retryAttempts: 3, // 重试次数
    description: '本地开发服务器'
  },

  // 测试环境配置
  testing: {
    name: '测试环境',
    baseUrl: 'http://************:18080',
    apiPath: '/api',
    timeout: 15000,
    retryAttempts: 2,
    description: '内部测试服务器'
  },

  // 生产环境配置
  production: {
    name: '生产环境',
    baseUrl: 'https://api.prompttools.com',
    apiPath: '/api/v1',
    timeout: 20000,
    retryAttempts: 3,
    description: '生产环境服务器'
  },

  // 自定义环境配置
  custom: {
    name: '自定义环境',
    baseUrl: 'http://*************:8080', // 可以修改为任意服务器地址
    apiPath: '/api',
    timeout: 12000,
    retryAttempts: 2,
    description: '自定义服务器地址'
  }
};

/**
 * 当前激活的环境
 * 修改此值可快速切换不同的服务器环境
 * 可选值: 'development', 'testing', 'production', 'custom'
 */
const CURRENT_ENVIRONMENT = 'testing';

/**
 * API 端点配置
 * 定义各种API接口的路径
 */
const API_ENDPOINTS = {
  // 提示词相关接口
  prompts: {
    list: '/prompts',           // 获取提示词列表
    create: '/prompts',         // 创建新提示词
    update: '/prompts/{id}',    // 更新提示词
    delete: '/prompts/{id}',    // 删除提示词
    search: '/prompts/search'   // 搜索提示词
  },

  // 标签相关接口
  tags: {
    list: '/prompts/meta/tags',     // 获取标签列表
    create: '/tags'                 // 创建新标签
  },

  // 用户相关接口
  user: {
    profile: '/user/profile',   // 用户信息
    settings: '/user/settings'  // 用户设置
  },

  // 系统相关接口
  system: {
    health: '/health',          // 健康检查
    version: '/version'         // 版本信息
  }
};

/**
 * 扩展功能配置
 */
const EXTENSION_CONFIG = {
  // 通知设置
  notifications: {
    enabled: true,              // 是否启用通知
    duration: 3000,             // 通知显示时长（毫秒）
    position: 'top-right'       // 通知位置
  },

  // 快捷键设置
  shortcuts: {
    quickAdd: 'Ctrl+Shift+P',   // 快速添加快捷键
    openExtension: 'Ctrl+Shift+O' // 打开扩展快捷键
  },

  // 界面设置
  ui: {
    theme: 'light',             // 主题: 'light' 或 'dark'
    language: 'zh-CN',          // 语言设置
    maxPromptPreview: 100       // 提示词预览最大字符数
  },

  // 数据设置
  data: {
    cacheEnabled: true,         // 是否启用缓存
    cacheExpiry: 300000,        // 缓存过期时间（毫秒）
    autoSync: true              // 是否自动同步
  }
};

/**
 * 获取当前环境配置
 * @returns {Object} 当前环境的配置对象
 */
function getCurrentEnvironment() {
  const env = SERVER_ENVIRONMENTS[CURRENT_ENVIRONMENT];
  if (!env) {
    console.warn(`未找到环境配置: ${CURRENT_ENVIRONMENT}，使用默认开发环境`);
    return SERVER_ENVIRONMENTS.development;
  }
  return env;
}

/**
 * 获取完整的API基础URL
 * @returns {string} 完整的API基础URL
 */
function getApiBaseUrl() {
  const env = getCurrentEnvironment();
  return env.baseUrl + env.apiPath;
}

/**
 * 获取特定API端点的完整URL
 * @param {string} category - API分类（如 'prompts', 'tags'）
 * @param {string} endpoint - 端点名称（如 'list', 'create'）
 * @param {Object} params - URL参数（用于替换路径中的占位符）
 * @returns {string} 完整的API端点URL
 */
function getApiEndpoint(category, endpoint, params = {}) {
  const baseUrl = getApiBaseUrl();
  const endpointPath = API_ENDPOINTS[category]?.[endpoint];
  
  if (!endpointPath) {
    throw new Error(`未找到API端点: ${category}.${endpoint}`);
  }

  // 替换URL中的参数占位符
  let fullPath = endpointPath;
  Object.keys(params).forEach(key => {
    fullPath = fullPath.replace(`{${key}}`, params[key]);
  });

  return baseUrl + fullPath;
}

/**
 * 获取请求配置
 * @returns {Object} 请求配置对象
 */
function getRequestConfig() {
  const env = getCurrentEnvironment();
  return {
    timeout: env.timeout,
    retryAttempts: env.retryAttempts,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'PromptTools-Extension/1.0.0'
    }
  };
}

/**
 * 验证服务器连接
 * @returns {Promise<boolean>} 连接是否成功
 */
async function validateServerConnection() {
  try {
    const healthUrl = getApiEndpoint('system', 'health');
    const config = getRequestConfig();
    
    const response = await fetch(healthUrl, {
      method: 'GET',
      headers: config.headers,
      signal: AbortSignal.timeout(config.timeout)
    });

    return response.ok;
  } catch (error) {
    console.error('服务器连接验证失败:', error);
    return false;
  }
}

// 导出配置对象和工具函数
// 注意：Chrome扩展中需要使用全局变量的方式来共享配置
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将配置添加到全局对象
  window.PromptToolsConfig = {
    environments: SERVER_ENVIRONMENTS,
    currentEnvironment: CURRENT_ENVIRONMENT,
    apiEndpoints: API_ENDPOINTS,
    extensionConfig: EXTENSION_CONFIG,
    getCurrentEnvironment,
    getApiBaseUrl,
    getApiEndpoint,
    getRequestConfig,
    validateServerConnection
  };
} else {
  // 在Node.js环境中使用模块导出
  module.exports = {
    environments: SERVER_ENVIRONMENTS,
    currentEnvironment: CURRENT_ENVIRONMENT,
    apiEndpoints: API_ENDPOINTS,
    extensionConfig: EXTENSION_CONFIG,
    getCurrentEnvironment,
    getApiBaseUrl,
    getApiEndpoint,
    getRequestConfig,
    validateServerConnection
  };
}
