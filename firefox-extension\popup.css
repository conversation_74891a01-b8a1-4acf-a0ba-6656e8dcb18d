/* Material Design 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 400px;
  min-height: 500px;
  max-height: 700px;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  color: #212121;
  background: #fafafa;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* 视图管理 */
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.view.hidden {
  display: none;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #ffffff;
  color: rgb(0, 0, 0);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo img {
  width: 28px;
  height: 28px;
  object-fit: contain;
}

.logo h1 {
  font-size: 20px;
  font-weight: 500;
  color: rgb(0, 0, 0);
  margin: 0;
}

.actions {
  display: flex;
  gap: 8px;
}

.btn-icon {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255,255,255,0.1);
  color: rgb(0, 0, 0);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  font-size: 20px;
}

.btn-icon:hover {
  background: rgba(212, 212, 212, 0.2);
}

.btn-icon#createBtn {
  background: rgba(228, 228, 228, 0.2);
}

.btn-icon#createBtn:hover {
  background: rgba(211, 211, 211, 0.3);
}

/* 搜索框样式 */
.search-container {
  display: flex;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 0px solid #e0e0e0;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 40px 0 0 40px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: #fff;
}

.search-input:focus {
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25,118,210,0.2);
}

.search-btn {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-left: none;
  border-radius: 0 40px 40px 0;
  background: #f5f5f5;
  color: #757575;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover {
  background: #eeeeee;
  color: #424242;
}

/* 分类过滤器 */
.category-filter {
  /* padding: 12px 20px; */
  background: #ffffff;
}

.category-buttons {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.category-buttons::-webkit-scrollbar {
  height: 4px;
}

.category-buttons::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.category-buttons::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.category-btn {
  flex-shrink: 0;
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  background: #fff;
  color: #424242;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
  white-space: nowrap;
}

.category-btn:hover {
  background: #f5f5f5;
  border-color: #bdbdbd;
}

.category-btn.active {
  background: #1976d2;
  color: white;
  border-color: #1976d2;
  box-shadow: 0 2px 4px rgba(25,118,210,0.3);
  transform: scale(1.05);
}

.category-btn:active {
  transform: scale(0.95);
}

/* 状态消息 */
.status-message {
  padding: 12px 20px;
  margin: 12px 20px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-message.success {
  background: #74a1db;
  color: white;
}

.status-message.error {
  background: #f44336;
  color: white;
}

/* 提示词容器 */
.prompts-container {
  flex: 1;
  overflow-y: auto;
  padding: 12px 0;
  background: #ffffff;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #757575;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-message {
  padding: 40px 20px;
  text-align: center;
  color: #757575;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-text h3 {
  color: #f44336;
  margin-bottom: 12px;
  font-weight: 500;
}

.error-text p {
  margin-bottom: 12px;
  line-height: 1.5;
  color: #616161;
}

.error-text code {
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Roboto Mono', 'Courier New', monospace;
  font-size: 13px;
  color: #424242;
}

.btn-primary {
  margin-top: 16px;
  padding: 10px 20px;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-primary:hover {
  background: #1565c0;
}

/* 提示词列表 */
.prompts-list {
  padding: 0 12px;
}

.prompt-item {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
  opacity: 0;
  animation: fadeInUp 0.4s ease-out forwards;
}

.prompt-item:nth-child(1) { animation-delay: 0.1s; }
.prompt-item:nth-child(2) { animation-delay: 0.2s; }
.prompt-item:nth-child(3) { animation-delay: 0.3s; }
.prompt-item:nth-child(4) { animation-delay: 0.4s; }
.prompt-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.prompt-item:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.prompt-item.copied {
  border-color: #6589d6;
  background: #f1f8e9;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.prompt-name {
  font-weight: 500;
  color: #212121;
  font-size: 16px;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.prompt-source {
  font-size: 12px;
  color: #757575;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 12px;
  white-space: nowrap;
}

.prompt-content {
  color: #424242;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prompt-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.prompt-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  flex: 1;
}

.tag {
  font-size: 12px;
  color: #1976d2;
  background: #e3f2fd;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #bbdefb;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: #f5f5f5;
  color: #757575;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: 16px;
}

.action-btn:hover {
  background: #eeeeee;
  color: #424242;
  transform: scale(1.1);
}

.action-btn.copy-btn {
  background: #e8f5e8;
  color: #4caf50;
}

.action-btn.copy-btn:hover {
  background: #c8e6c9;
}

.action-btn.copy-btn:active {
  transform: scale(0.95);
}

.action-btn.edit-btn:hover {
  background: #fff3e0;
  color: #ff9800;
}

.action-btn.delete-btn:hover {
  background: #ffebee;
  color: #f44336;
}

.copy-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #4caf50;
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.copy-indicator.show {
  opacity: 1;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
  color: #757575;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin-bottom: 12px;
  color: #424242;
  font-weight: 500;
}

.empty-state p {
  color: #757575;
  line-height: 1.5;
}

/* 底部 */
.footer {
  padding: 16px 20px;
  background: #fff;
  border-top: 1px solid #e0e0e0;
}

.tips {
  font-size: 13px;
  color: #757575;
  text-align: center;
  line-height: 1.4;
}

/* 右键菜单 */
.context-menu {
  position: fixed;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 1px solid #e0e0e0;
  z-index: 1000;
  min-width: 150px;
  overflow: hidden;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #424242;
}

.context-menu-item:hover {
  background: #f5f5f5;
}

.context-menu-item i {
  font-size: 16px;
  color: #757575;
}

/* 确认对话框 */
.confirm-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
}

.confirm-content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  max-width: 320px;
  width: 90%;
  box-shadow: 0 8px 24px rgba(0,0,0,0.2);
  position: relative;
  z-index: 1;
}

.confirm-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.confirm-header i {
  font-size: 24px;
  color: #ff9800;
}

.confirm-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #212121;
}

.confirm-content p {
  margin-bottom: 20px;
  color: #424242;
  line-height: 1.5;
}

.confirm-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn-cancel {
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: #fff;
  color: #424242;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-cancel:hover {
  background: #f5f5f5;
}

.btn-delete {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #f44336;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-delete:hover {
  background: #d32f2f;
}

/* 工具类 */
.hidden {
  display: none !important;
}

/* 滚动条样式 */
.prompts-container::-webkit-scrollbar {
  width: 8px;
}

.prompts-container::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.prompts-container::-webkit-scrollbar-thumb {
  background: #ffffff;
  border-radius: 4px;
}

.prompts-container::-webkit-scrollbar-thumb:hover {
  background: #9e9e9e;
}

.category-buttons::-webkit-scrollbar {
  height: 4px;
}

.category-buttons::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.category-buttons::-webkit-scrollbar-thumb {
  background: #bdbdbd;
  border-radius: 2px;
}

/* 编辑视图样式 */
.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #1976d2;
  color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.edit-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.edit-header-left h2 {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  color: white;
}

.btn-save {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-save:hover {
  background: #45a049;
}

.btn-save:active {
  transform: translateY(1px);
}

/* 编辑容器 */
.edit-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #fafafa;
}

.edit-form {
  max-width: 100%;
}

/* 表单组 */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #424242;
  font-size: 14px;
}

.required {
  color: #f44336;
  margin-right: 4px;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: #fff;
  font-family: inherit;
}

.form-input:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25,118,210,0.2);
}

/* 文本域样式 */
.textarea-container {
  position: relative;
}

.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: #fff;
}

.form-textarea:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25,118,210,0.2);
}

.char-counter {
  position: absolute;
  bottom: 6px;
  right: 10px;
  font-size: 12px;
  color: #757575;
  background: rgba(255,255,255,0.9);
  padding: 2px 6px;
  border-radius: 2px;
  pointer-events: none;
}

/* 响应式调整 */
@media (max-width: 420px) {
  body {
    width: 360px;
  }

  .header {
    padding: 12px 16px;
  }

  .search-container {
    padding: 12px 16px;
  }

  .category-filter {
    padding: 8px 16px;
  }

  .prompts-list {
    padding: 0 8px;
  }

  .prompt-item {
    padding: 12px;
  }

  .prompt-content {
    -webkit-line-clamp: 2;
  }

  .action-buttons {
    gap: 6px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .edit-header {
    padding: 12px 16px;
  }

  .edit-container {
    padding: 16px;
  }

  .form-group {
    margin-bottom: 14px;
  }
}
