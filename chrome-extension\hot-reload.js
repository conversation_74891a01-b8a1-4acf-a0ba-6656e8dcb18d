// Chrome Extension Hot Reload Script
// 用于开发环境的热重载功能

class ExtensionHotReload {
  constructor() {
    this.isEnabled = true;
    this.checkInterval = 1000; // 检查间隔（毫秒）
    this.lastModified = null;
    this.reloadServer = 'http://localhost:8080'; // 热重载服务器地址
    
    // 只在开发环境启用
    if (this.isDevelopment()) {
      this.init();
    }
  }

  isDevelopment() {
    // 检查是否为开发环境
    try {
      return chrome.runtime.getManifest().version.includes('dev') ||
             (typeof window !== 'undefined' && window.location.hostname === 'localhost') ||
             chrome.runtime.id === chrome.runtime.getManifest().key;
    } catch (error) {
      // 在Service Worker中，简单返回false禁用热重载
      return false;
    }
  }

  init() {
    console.log('🔥 Chrome Extension Hot Reload 已启用');
    this.startWatching();
    this.setupReloadListener();
  }

  startWatching() {
    // 定期检查扩展是否需要重载
    setInterval(() => {
      this.checkForUpdates();
    }, this.checkInterval);
  }

  async checkForUpdates() {
    try {
      // 检查热重载服务器状态
      const response = await fetch(`${this.reloadServer}/status`, {
        method: 'GET',
        cache: 'no-cache'
      });

      if (response.ok) {
        const data = await response.json();
        
        if (this.lastModified && data.lastModified > this.lastModified) {
          console.log('🔄 检测到文件变化，重新加载扩展...');
          this.reloadExtension();
        }
        
        this.lastModified = data.lastModified;
      }
    } catch (error) {
      // 静默处理错误，避免在生产环境中产生噪音
      if (this.isDevelopment()) {
        console.debug('热重载服务器连接失败:', error.message);
      }
    }
  }

  setupReloadListener() {
    // 监听来自开发服务器的重载消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'hotReload') {
          console.log('🔄 收到热重载信号');
          this.reloadExtension();
        }
      });
    }
  }

  reloadExtension() {
    try {
      // 尝试重新加载扩展
      if (chrome.runtime && chrome.runtime.reload) {
        chrome.runtime.reload();
      } else if (chrome.management) {
        // 备用方法：通过管理API重载
        chrome.management.getSelf((extensionInfo) => {
          chrome.management.setEnabled(extensionInfo.id, false, () => {
            chrome.management.setEnabled(extensionInfo.id, true);
          });
        });
      } else {
        // 最后的备用方法：刷新页面
        window.location.reload();
      }
    } catch (error) {
      console.error('扩展重载失败:', error);
      // 如果所有方法都失败，提示用户手动重载
      console.warn('⚠️ 请手动重新加载扩展：chrome://extensions/');
    }
  }

  // 手动触发重载的方法
  static triggerReload() {
    if (window.extensionHotReload) {
      window.extensionHotReload.reloadExtension();
    }
  }
}

// 只在开发环境中初始化热重载
if (typeof window !== 'undefined') {
  window.extensionHotReload = new ExtensionHotReload();
}

// 导出给其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ExtensionHotReload;
}
