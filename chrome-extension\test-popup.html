<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试 Popup 界面</title>
  <link rel="stylesheet" href="popup.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    body {
      margin: 20px;
      background: #f0f0f0;
    }
    .test-container {
      display: inline-block;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      border-radius: 8px;
      overflow: hidden;
    }
  </style>
</head>
<body>
  <h1>Prompt Tools 扩展界面测试</h1>
  <p>这是一个测试页面，用于预览Chrome扩展的popup界面效果。</p>
  
  <div class="test-container">
    <div class="container">
      <!-- 列表视图 -->
      <div id="listView" class="view">
        <!-- 头部 -->
        <div class="header">
          <div class="logo">
            <img src="icons/icon32.png" alt="Prompt Hub">
            <h1>Prompt Hub</h1>
          </div>
          <div class="actions">
            <button id="createBtn" class="btn-icon" title="创建提示词">
              <i class="material-icons">add</i>
            </button>
            <button id="refreshBtn" class="btn-icon" title="刷新">
              <i class="material-icons">refresh</i>
            </button>
            <button id="openWebBtn" class="btn-icon" title="打开网页版">
              <i class="material-icons">open_in_new</i>
            </button>
          </div>
        </div>

      <!-- 搜索框 -->
      <div class="search-container">
        <input type="text" id="searchInput" placeholder="搜索提示词..." class="search-input">
        <button id="searchBtn" class="search-btn">
          <i class="material-icons">search</i>
        </button>
      </div>

      <!-- 分类过滤器 -->
      <div class="category-filter">
        <div class="category-buttons">
          <button class="category-btn active" data-category="all">全部</button>
          <button class="category-btn" data-category="邮件">邮件 (3)</button>
          <button class="category-btn" data-category="商务">商务 (2)</button>
          <button class="category-btn" data-category="写作">写作 (2)</button>
          <button class="category-btn" data-category="工作">工作 (1)</button>
          <button class="category-btn" data-category="效率">效率 (1)</button>
        </div>
      </div>

      <!-- 状态显示 -->
      <div id="statusMessage" class="status-message success">✅ 测试状态消息</div>

      <!-- 提示词列表 -->
      <div class="prompts-container">
        <div id="promptsList" class="prompts-list">
          <!-- 示例提示词 -->
          <div class="prompt-item" data-id="1">
            <div class="prompt-header">
              <div class="prompt-name">专业邮件写作助手</div>
              <div class="prompt-source">工作</div>
            </div>
            <div class="prompt-content">帮我写一封专业的商务邮件，内容要求：[具体要求]。请确保邮件语气正式、礼貌，结构清晰，包含适当的开头和结尾。</div>
            <div class="prompt-actions">
              <div class="prompt-tags">
                <span class="tag">邮件</span>
                <span class="tag">商务</span>
                <span class="tag">写作</span>
              </div>
              <div class="action-buttons">
                <button class="action-btn copy-btn" title="复制">
                  <i class="material-icons">content_copy</i>
                </button>
                <button class="action-btn edit-btn" title="编辑">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" title="删除">
                  <i class="material-icons">delete</i>
                </button>
              </div>
            </div>
            <div class="copy-indicator">已复制!</div>
          </div>

          <div class="prompt-item" data-id="2">
            <div class="prompt-header">
              <div class="prompt-name">代码审查专家</div>
            </div>
            <div class="prompt-content">请对以下代码进行详细审查，包括：1. 代码质量和可读性 2. 潜在的bug和安全问题 3. 性能优化建议 4. 最佳实践建议</div>
            <div class="prompt-actions">
              <div class="prompt-tags">
                <span class="tag">编程</span>
                <span class="tag">代码审查</span>
                <span class="tag">技术</span>
              </div>
              <div class="action-buttons">
                <button class="action-btn copy-btn" title="复制">
                  <i class="material-icons">content_copy</i>
                </button>
                <button class="action-btn edit-btn" title="编辑">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" title="删除">
                  <i class="material-icons">delete</i>
                </button>
              </div>
            </div>
            <div class="copy-indicator">已复制!</div>
          </div>
        </div>
      </div>

        <!-- 底部 -->
        <div class="footer">
          <div class="tips">
            💡 右键选中文本可快速添加提示词
          </div>
        </div>
      </div>

      <!-- 编辑视图 -->
      <div id="editView" class="view hidden">
        <!-- 编辑头部 -->
        <div class="edit-header">
          <div class="edit-header-left">
            <button id="backToListBtn" class="btn-icon" title="返回列表">
              <i class="material-icons">arrow_back</i>
            </button>
            <h2 id="editTitle">编辑提示词</h2>
          </div>
          <div class="edit-header-right">
            <button id="savePromptBtn" class="btn-save">
              <i class="material-icons">save</i>
              <span>保存</span>
            </button>
          </div>
        </div>

        <!-- 编辑表单 -->
        <div class="edit-container">
          <form id="editForm" class="edit-form">
            <div class="form-group">
              <label for="editPromptName" class="form-label">
                <span class="required">*</span>
                标题
              </label>
              <input
                type="text"
                id="editPromptName"
                name="name"
                class="form-input"
                placeholder="输入提示词标题"
                value="专业邮件写作助手"
                required
              >
            </div>

            <div class="form-group">
              <label for="editPromptContent" class="form-label">
                <span class="required">*</span>
                内容
              </label>
              <div class="textarea-container">
                <textarea
                  id="editPromptContent"
                  name="content"
                  class="form-textarea"
                  placeholder="输入提示词内容"
                  rows="6"
                  required
                >帮我写一封专业的商务邮件，内容要求：[具体要求]。请确保邮件语气正式、礼貌，结构清晰，包含适当的开头和结尾。</textarea>
                <div class="char-counter">
                  <span id="editCharCount">65</span> 字符
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="editPromptTags" class="form-label">标签</label>
              <input
                type="text"
                id="editPromptTags"
                name="tags"
                class="form-input"
                placeholder="用逗号分隔多个标签，如：工作,效率,AI"
                value="邮件, 商务, 写作"
              >
            </div>

            <div class="form-group">
              <label for="editPromptSource" class="form-label">来源</label>
              <input
                type="text"
                id="editPromptSource"
                name="source"
                class="form-input"
                placeholder="来源链接或描述"
                value="工作"
              >
            </div>

            <div class="form-group">
              <label for="editPromptNotes" class="form-label">备注</label>
              <textarea
                id="editPromptNotes"
                name="notes"
                class="form-textarea"
                placeholder="添加备注信息..."
                rows="2"
              ></textarea>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- 右键菜单 -->
  <div id="contextMenu" class="context-menu hidden">
    <div class="context-menu-item" id="copyAndEditBtn">
      <i class="material-icons">content_copy</i>
      <span>复制并编辑</span>
    </div>
  </div>

  <!-- 确认对话框 -->
  <div id="confirmDialog" class="confirm-dialog hidden">
    <div class="confirm-overlay"></div>
    <div class="confirm-content">
      <div class="confirm-header">
        <i class="material-icons">warning</i>
        <h3>确认删除</h3>
      </div>
      <p id="confirmMessage">确定要删除这个提示词吗？</p>
      <div class="confirm-actions">
        <button id="confirmCancel" class="btn-cancel">取消</button>
        <button id="confirmDelete" class="btn-delete">删除</button>
      </div>
    </div>
  </div>

  <script>
    // 简单的测试脚本
    document.addEventListener('DOMContentLoaded', () => {
      // 视图切换
      function showListView() {
        document.getElementById('listView').classList.remove('hidden');
        document.getElementById('editView').classList.add('hidden');
      }

      function showEditView() {
        document.getElementById('listView').classList.add('hidden');
        document.getElementById('editView').classList.remove('hidden');
      }

      // 创建按钮
      document.getElementById('createBtn').addEventListener('click', () => {
        showEditView();
        document.getElementById('editTitle').textContent = '创建提示词';
        document.getElementById('savePromptBtn').innerHTML = '<i class="material-icons">add</i><span>创建</span>';
        document.getElementById('editForm').reset();
      });

      // 返回按钮
      document.getElementById('backToListBtn').addEventListener('click', () => {
        showListView();
      });

      // 分类按钮点击
      document.querySelectorAll('.category-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
          e.target.classList.add('active');
        });
      });

      // 复制按钮
      document.querySelectorAll('.copy-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation();
          const item = e.target.closest('.prompt-item');
          const indicator = item.querySelector('.copy-indicator');
          item.classList.add('copied');
          indicator.classList.add('show');
          setTimeout(() => {
            item.classList.remove('copied');
            indicator.classList.remove('show');
          }, 1500);
        });
      });

      // 编辑按钮
      document.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation();
          showEditView();
          document.getElementById('editTitle').textContent = '编辑提示词';
          document.getElementById('savePromptBtn').innerHTML = '<i class="material-icons">save</i><span>保存</span>';
        });
      });

      // 右键菜单
      document.querySelectorAll('.prompt-item').forEach(item => {
        item.addEventListener('contextmenu', (e) => {
          e.preventDefault();
          const menu = document.getElementById('contextMenu');
          menu.style.left = e.pageX + 'px';
          menu.style.top = e.pageY + 'px';
          menu.classList.remove('hidden');
        });
      });

      // 复制并编辑
      document.getElementById('copyAndEditBtn').addEventListener('click', () => {
        showEditView();
        document.getElementById('editTitle').textContent = '创建提示词';
        document.getElementById('savePromptBtn').innerHTML = '<i class="material-icons">add</i><span>创建</span>';
        document.getElementById('editPromptContent').value = '帮我写一封专业的商务邮件，内容要求：[具体要求]。请确保邮件语气正式、礼貌，结构清晰，包含适当的开头和结尾。';
        document.getElementById('editPromptName').value = '';
        document.getElementById('editPromptTags').value = '';
        document.getElementById('contextMenu').classList.add('hidden');
      });

      // 点击其他地方关闭菜单
      document.addEventListener('click', () => {
        document.getElementById('contextMenu').classList.add('hidden');
      });

      // 确认对话框
      document.getElementById('confirmCancel').addEventListener('click', () => {
        document.getElementById('confirmDialog').classList.add('hidden');
      });

      document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation();
          document.getElementById('confirmDialog').classList.remove('hidden');
        });
      });

      document.querySelector('.confirm-overlay').addEventListener('click', () => {
        document.getElementById('confirmDialog').classList.add('hidden');
      });

      // 字符计数
      document.getElementById('editPromptContent').addEventListener('input', (e) => {
        document.getElementById('editCharCount').textContent = e.target.value.length;
      });
    });
  </script>
</body>
</html>
