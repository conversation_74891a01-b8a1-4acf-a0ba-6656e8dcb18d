<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>复制功能测试页面</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    .test-section {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    .test-button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
      font-size: 14px;
    }
    .test-button:hover {
      background: #0056b3;
    }
    .test-result {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      margin: 10px 0;
      min-height: 50px;
    }
    .success {
      border-color: #28a745;
      background: #d4edda;
      color: #155724;
    }
    .error {
      border-color: #dc3545;
      background: #f8d7da;
      color: #721c24;
    }
    .warning {
      border-color: #ffc107;
      background: #fff3cd;
      color: #856404;
    }
    .test-content {
      background: #e3f2fd;
      border: 1px solid #2196f3;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
      font-family: monospace;
      font-size: 12px;
    }
    .paste-area {
      width: 100%;
      height: 100px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <h1>🔧 复制功能测试页面</h1>
  
  <div class="test-section">
    <h2>📋 测试说明</h2>
    <p>这个页面用于测试Firefox扩展中的复制功能。请按照以下步骤进行测试：</p>
    <ol>
      <li>点击下面的测试按钮</li>
      <li>观察测试结果</li>
      <li>在粘贴区域按 Ctrl+V 验证复制是否成功</li>
    </ol>
  </div>

  <div class="test-section">
    <h2>🧪 复制方法测试</h2>
    
    <h3>测试内容：</h3>
    <div class="test-content" id="testContent">
这是一个用于测试复制功能的示例文本。
包含中文字符、English characters、数字123、特殊符号!@#$%^&*()
以及换行符和多行内容。

这个文本应该能够被正确复制到剪贴板中。
    </div>
    
    <h3>测试按钮：</h3>
    <button class="test-button" onclick="testExecCommand()">测试 execCommand 方法</button>
    <button class="test-button" onclick="testClipboardAPI()">测试 Clipboard API</button>
    <button class="test-button" onclick="testCombinedMethod()">测试组合方法</button>
    <button class="test-button" onclick="clearResults()">清除结果</button>
    
    <h3>测试结果：</h3>
    <div id="testResults" class="test-result">
      点击上面的按钮开始测试...
    </div>
    
    <h3>粘贴验证区域：</h3>
    <textarea class="paste-area" placeholder="在这里按 Ctrl+V 粘贴，验证复制是否成功..."></textarea>
  </div>

  <div class="test-section">
    <h2>🔍 浏览器环境信息</h2>
    <div id="browserInfo" class="test-result"></div>
  </div>

  <script>
    // 获取测试内容
    const testContent = document.getElementById('testContent').textContent.trim();
    
    // 显示浏览器信息
    function showBrowserInfo() {
      const info = document.getElementById('browserInfo');
      info.innerHTML = `
        <strong>用户代理：</strong>${navigator.userAgent}<br>
        <strong>平台：</strong>${navigator.platform}<br>
        <strong>语言：</strong>${navigator.language}<br>
        <strong>Clipboard API 支持：</strong>${navigator.clipboard ? '✅ 支持' : '❌ 不支持'}<br>
        <strong>writeText 方法：</strong>${navigator.clipboard && navigator.clipboard.writeText ? '✅ 支持' : '❌ 不支持'}<br>
        <strong>execCommand 支持：</strong>${document.execCommand ? '✅ 支持' : '❌ 不支持'}
      `;
    }
    
    // 显示测试结果
    function showResult(message, type = 'success') {
      const results = document.getElementById('testResults');
      const timestamp = new Date().toLocaleTimeString();
      results.className = `test-result ${type}`;
      results.innerHTML = `[${timestamp}] ${message}`;
    }
    
    // 测试 execCommand 方法
    function testExecCommand() {
      try {
        console.log('开始测试 execCommand 方法');
        
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = testContent;
        
        // 设置样式
        textArea.style.position = 'fixed';
        textArea.style.left = '0';
        textArea.style.top = '0';
        textArea.style.width = '1px';
        textArea.style.height = '1px';
        textArea.style.padding = '0';
        textArea.style.border = 'none';
        textArea.style.outline = 'none';
        textArea.style.boxShadow = 'none';
        textArea.style.background = 'transparent';
        textArea.style.opacity = '0';
        
        // 添加到DOM
        document.body.appendChild(textArea);
        
        // 聚焦并选择
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, testContent.length);
        
        // 执行复制
        const success = document.execCommand('copy');
        
        // 清理
        document.body.removeChild(textArea);
        
        if (success) {
          showResult('✅ execCommand 复制成功！请在粘贴区域验证。', 'success');
          console.log('execCommand 复制成功');
        } else {
          showResult('❌ execCommand 复制失败：返回 false', 'error');
          console.log('execCommand 复制失败');
        }
        
      } catch (error) {
        showResult(`❌ execCommand 复制出错：${error.message}`, 'error');
        console.error('execCommand 复制出错:', error);
      }
    }
    
    // 测试 Clipboard API
    async function testClipboardAPI() {
      try {
        console.log('开始测试 Clipboard API');
        
        if (!navigator.clipboard) {
          showResult('❌ 浏览器不支持 Clipboard API', 'error');
          return;
        }
        
        if (!navigator.clipboard.writeText) {
          showResult('❌ 浏览器不支持 writeText 方法', 'error');
          return;
        }
        
        await navigator.clipboard.writeText(testContent);
        showResult('✅ Clipboard API 复制成功！请在粘贴区域验证。', 'success');
        console.log('Clipboard API 复制成功');
        
      } catch (error) {
        showResult(`❌ Clipboard API 复制出错：${error.message}`, 'error');
        console.error('Clipboard API 复制出错:', error);
      }
    }
    
    // 测试组合方法（模拟扩展中的逻辑）
    async function testCombinedMethod() {
      try {
        console.log('开始测试组合方法');
        let copySuccess = false;
        
        // 首先尝试 execCommand
        try {
          const textArea = document.createElement('textarea');
          textArea.value = testContent;
          textArea.style.position = 'fixed';
          textArea.style.left = '0';
          textArea.style.top = '0';
          textArea.style.width = '1px';
          textArea.style.height = '1px';
          textArea.style.opacity = '0';
          
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          
          const success = document.execCommand('copy');
          document.body.removeChild(textArea);
          
          if (success) {
            copySuccess = true;
            showResult('✅ 组合方法成功（使用 execCommand）！请在粘贴区域验证。', 'success');
            console.log('组合方法：execCommand 成功');
          }
        } catch (execError) {
          console.warn('组合方法：execCommand 失败', execError);
        }
        
        // 如果 execCommand 失败，尝试 Clipboard API
        if (!copySuccess && navigator.clipboard && navigator.clipboard.writeText) {
          try {
            await navigator.clipboard.writeText(testContent);
            copySuccess = true;
            showResult('✅ 组合方法成功（使用 Clipboard API）！请在粘贴区域验证。', 'success');
            console.log('组合方法：Clipboard API 成功');
          } catch (clipboardError) {
            console.warn('组合方法：Clipboard API 失败', clipboardError);
          }
        }
        
        if (!copySuccess) {
          showResult('❌ 组合方法失败：所有复制方法都不可用', 'error');
          console.log('组合方法：所有方法都失败');
        }
        
      } catch (error) {
        showResult(`❌ 组合方法出错：${error.message}`, 'error');
        console.error('组合方法出错:', error);
      }
    }
    
    // 清除结果
    function clearResults() {
      const results = document.getElementById('testResults');
      results.className = 'test-result';
      results.innerHTML = '结果已清除，点击按钮开始新的测试...';
    }
    
    // 页面加载时显示浏览器信息
    showBrowserInfo();
    
    console.log('复制功能测试页面已加载');
    console.log('测试内容长度:', testContent.length);
  </script>
</body>
</html>
